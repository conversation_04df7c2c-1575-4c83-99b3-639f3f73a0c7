#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新现有Excel文件的天气数据
"""

import pandas as pd
from weather_scraper import WeatherScraper
from datetime import datetime
import argparse

# 城市名称映射（中文 -> 拼音）
CITY_MAPPING = {
    '海宁': 'haining',
    '杭州': 'hangzhou', 
    '金华': 'jinhua',
    '宁波': 'ningbo',
    '衢州': 'quzhou',
    '台州': 'taizhou',
    '温州': 'wenzhou',
    '诸暨': 'zhuji',
    '嘉兴': 'jiaxing',
    '绍兴': 'shaoxing',
    '湖州': 'huzhou',
    '丽水': 'lishui',
    '舟山': 'zhoushan'
}

# 反向映射（拼音 -> 中文）
PINYIN_TO_CHINESE = {v: k for k, v in CITY_MAPPING.items()}

class WeatherExcelUpdater:
    def __init__(self):
        self.scraper = WeatherScraper()
    
    def update_excel_file(self, excel_file, year, month, output_file=None):
        """
        更新Excel文件中的天气数据
        
        Args:
            excel_file (str): 现有Excel文件路径
            year (int): 年份
            month (int): 月份
            output_file (str): 输出文件路径，如果为None则覆盖原文件
        """
        try:
            # 读取现有Excel文件
            df = pd.read_excel(excel_file)
            print(f"读取到 {len(df)} 条现有数据")
            
            # 获取现有的城市列表（中文名）
            existing_cities = df['城市'].unique()
            print(f"现有城市: {list(existing_cities)}")
            
            # 爬取新数据
            all_new_data = []
            
            for chinese_city in existing_cities:
                if chinese_city in CITY_MAPPING:
                    pinyin_city = CITY_MAPPING[chinese_city]
                    print(f"\n正在爬取 {chinese_city}({pinyin_city}) {year}年{month}月数据...")
                    
                    city_data = self.scraper.get_weather_data(pinyin_city, year, month)
                    
                    # 将拼音城市名转换为中文
                    for record in city_data:
                        record['城市'] = chinese_city
                    
                    all_new_data.extend(city_data)
                    print(f"获取到 {len(city_data)} 条数据")
                else:
                    print(f"警告: 未找到城市 {chinese_city} 的拼音映射")
            
            if all_new_data:
                # 创建新的DataFrame
                new_df = pd.DataFrame(all_new_data)
                
                # 确保列的顺序正确
                columns_order = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)']
                new_df = new_df.reindex(columns=columns_order)
                
                # 保存文件
                output_path = output_file if output_file else excel_file
                new_df.to_excel(output_path, index=False, engine='openpyxl')
                
                print(f"\n成功更新数据到: {output_path}")
                print(f"总共 {len(new_df)} 条数据")
                
                # 显示统计信息
                print("\n城市数据统计:")
                print(new_df['城市'].value_counts())
                
            else:
                print("未获取到任何新数据")
                
        except Exception as e:
            print(f"更新失败: {e}")
    
    def add_cities_to_excel(self, excel_file, new_cities, year, month, output_file=None):
        """
        向Excel文件添加新城市的数据
        
        Args:
            excel_file (str): 现有Excel文件路径
            new_cities (list): 新城市列表（中文名）
            year (int): 年份
            month (int): 月份
            output_file (str): 输出文件路径
        """
        try:
            # 读取现有数据
            existing_df = pd.read_excel(excel_file)
            print(f"现有数据: {len(existing_df)} 条")
            
            # 爬取新城市数据
            new_data = []
            
            for chinese_city in new_cities:
                if chinese_city in CITY_MAPPING:
                    pinyin_city = CITY_MAPPING[chinese_city]
                    print(f"\n正在爬取 {chinese_city}({pinyin_city}) {year}年{month}月数据...")
                    
                    city_data = self.scraper.get_weather_data(pinyin_city, year, month)
                    
                    # 将拼音城市名转换为中文
                    for record in city_data:
                        record['城市'] = chinese_city
                    
                    new_data.extend(city_data)
                    print(f"获取到 {len(city_data)} 条数据")
                else:
                    print(f"警告: 未找到城市 {chinese_city} 的拼音映射")
            
            if new_data:
                # 合并数据
                new_df = pd.DataFrame(new_data)
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                
                # 确保列的顺序正确
                columns_order = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)']
                combined_df = combined_df.reindex(columns=columns_order)
                
                # 保存文件
                output_path = output_file if output_file else excel_file
                combined_df.to_excel(output_path, index=False, engine='openpyxl')
                
                print(f"\n成功添加数据到: {output_path}")
                print(f"总共 {len(combined_df)} 条数据")
                
                # 显示统计信息
                print("\n城市数据统计:")
                print(combined_df['城市'].value_counts())
                
            else:
                print("未获取到任何新数据")
                
        except Exception as e:
            print(f"添加失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='更新Excel天气数据')
    parser.add_argument('--excel', type=str, required=True, help='Excel文件路径')
    parser.add_argument('--year', type=int, required=True, help='年份')
    parser.add_argument('--month', type=int, required=True, help='月份')
    parser.add_argument('--output', type=str, help='输出文件路径（可选）')
    parser.add_argument('--add-cities', type=str, nargs='+', help='添加新城市（中文名）')
    
    args = parser.parse_args()
    
    updater = WeatherExcelUpdater()
    
    if args.add_cities:
        # 添加新城市
        updater.add_cities_to_excel(args.excel, args.add_cities, args.year, args.month, args.output)
    else:
        # 更新现有城市
        updater.update_excel_file(args.excel, args.year, args.month, args.output)

if __name__ == "__main__":
    main()
