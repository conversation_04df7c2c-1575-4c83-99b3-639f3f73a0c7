# 🌤️ 天气数据爬取工具 - 使用演示

## 🎯 工具概述

这是一个完备的交互式天气数据爬取工具，支持从天气24网站爬取历史天气数据，并按照标准Excel格式输出。

## 🚀 快速开始

### 1. 启动交互式界面

```bash
# 激活虚拟环境
source venv/bin/activate

# 启动交互式使用指南
python 使用指南.py
```

### 2. 主菜单界面

```
==================================================
🌤️  天气数据爬取工具
==================================================
1. 爬取单个城市天气数据
2. 爬取多个城市天气数据
3. 更新现有Excel文件
4. 向Excel文件添加新城市
5. 查看支持的城市列表
6. 批量爬取多个月份数据
7. 预览和验证Excel文件
8. 测试网络连接
0. 退出
==================================================
```

## 📋 功能详细演示

### 功能1: 爬取单个城市天气数据

**操作步骤:**
1. 选择 `1`
2. 输入城市名称（拼音）: `jiaxing`
3. 输入年份: `2024`
4. 输入月份: `3`
5. 输入输出文件名: `嘉兴2024年03月天气.xlsx`

**演示结果:**
```
📍 爬取单个城市天气数据
支持的城市示例: jiaxing(嘉兴), hangzhou(杭州), shanghai(上海), beijing(北京)
请输入城市名称（拼音，如jiaxing）: jiaxing
请输入年份（如2024）: 2024
请输入月份（1-12）: 3
请输入输出文件名（如天气数据.xlsx）: 嘉兴2024年03月天气.xlsx

开始爬取 jiaxing 2024年3月的天气数据...
正在爬取: https://www.tianqi24.com/jiaxing/history202403.html
成功获取 31 条天气数据
数据已保存到: 嘉兴2024年03月天气.xlsx
✅ 成功保存 31 条数据到 嘉兴2024年03月天气.xlsx
```

### 功能2: 爬取多个城市天气数据

**操作步骤:**
1. 选择 `2`
2. 输入城市名称: `jiaxing hangzhou ningbo`
3. 输入年份: `2024`
4. 输入月份: `3`
5. 输入输出文件名: `浙江三市2024年03月天气.xlsx`

### 功能3: 更新现有Excel文件

**操作步骤:**
1. 选择 `3`
2. 输入Excel文件路径: `八月地方天气.xlsx`
3. 输入年份: `2024`
4. 输入月份: `3`
5. 输入输出文件名: `更新后的天气数据.xlsx`

**演示结果:**
- 自动读取现有文件中的城市列表
- 批量爬取所有城市的指定月份数据
- 保持原有的Excel格式和列结构

### 功能4: 向Excel文件添加新城市

**操作步骤:**
1. 选择 `4`
2. 输入Excel文件路径: `八月地方天气.xlsx`
3. 输入要添加的城市: `嘉兴 绍兴`
4. 输入年份: `2024`
5. 输入月份: `3`
6. 输入输出文件名: `添加新城市后的天气.xlsx`

### 功能5: 查看支持的城市列表

**演示结果:**
```
🗺️ 支持的城市列表

中文名 -> 拼音名
------------------------------
海宁     -> haining
杭州     -> hangzhou
金华     -> jinhua
宁波     -> ningbo
衢州     -> quzhou
台州     -> taizhou
温州     -> wenzhou
诸暨     -> zhuji
嘉兴     -> jiaxing
绍兴     -> shaoxing
湖州     -> huzhou
丽水     -> lishui
舟山     -> zhoushan

总共支持 13 个城市
```

### 功能6: 批量爬取多个月份数据

**操作步骤:**
1. 选择 `6`
2. 输入城市名称: `jiaxing`
3. 输入年份: `2024`
4. 输入开始月份: `1`
5. 输入结束月份: `3`
6. 输入输出文件名: `嘉兴2024年1-3月天气.xlsx`

### 功能7: 预览和验证Excel文件

**演示结果:**
```
📊 预览和验证Excel文件

📋 文件基本信息:
   文件名: 测试单城市爬取.xlsx
   数据行数: 31
   数据列数: 7
   文件大小: 6197 字节

🏙️ 城市统计:
   jiaxing: 31 条数据

📅 日期范围:
   最早日期: 2024-03-01
   最晚日期: 2024-03-31
   日期跨度: 31 天

🌡️ 温度统计:
   最高温度: 30°C
   最低温度: -3°C
   平均最高温度: 17.3°C
   平均最低温度: 6.7°C

⚠️ 数据质量检查:
   ✅ 无缺失数据
   ✅ 温度数据逻辑正确
```

### 功能8: 测试网络连接

**演示结果:**
```
🌐 测试网络连接
测试是否能正常访问天气网站...
正在测试访问: https://www.tianqi24.com/jiaxing/history202403.html
✅ 网络连接正常
   响应时间: 0.07 秒
   状态码: 200
   页面大小: 31275 字节
   ✅ 页面内容正常
```

## 🎯 输出数据格式

生成的Excel文件包含以下7列，完全符合要求：

| 列名 | 数据类型 | 示例 |
|------|----------|------|
| 城市 | 文本 | jiaxing |
| 日期 | 日期 | 2024-03-01 |
| 日期类型 | 文本 | 星期五 |
| 白天天气 | 文本 | 晴 |
| 晚上天气 | 文本 | 多云 |
| 最高温度(°C) | 数值 | 10 |
| 最低温度(°C) | 数值 | -2 |

## ✨ 工具特色

### 🛡️ 智能输入验证
- 年份范围验证（2000-2025）
- 月份范围验证（1-12）
- 文件路径存在性检查
- 城市名称支持性检查

### 🔧 错误处理
- 网络连接异常处理
- 文件读写异常处理
- 数据解析异常处理
- 用户输入异常处理

### 📊 数据质量保证
- 自动数据完整性检查
- 温度逻辑验证
- 日期连续性检查
- 缺失数据统计

### 🎨 用户体验
- 友好的交互界面
- 详细的操作提示
- 实时进度反馈
- 彩色状态显示

## 🎉 测试验证

所有功能已通过综合测试：

- ✅ 菜单显示功能正常
- ✅ 城市映射功能正常（支持13个城市）
- ✅ Excel预览功能正常
- ✅ 网络连接测试正常
- ✅ 输入验证功能正常
- ✅ 错误处理功能正常

## 🚀 开始使用

现在你可以运行以下命令开始使用：

```bash
python 使用指南.py
```

然后根据菜单提示选择相应的功能即可！
