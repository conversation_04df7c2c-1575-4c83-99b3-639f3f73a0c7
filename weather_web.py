#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据爬取工具 - Web版本
提供网页界面，用户可以通过浏览器使用
"""

from flask import Flask, render_template, request, jsonify, send_file, flash, redirect, url_for
import os
import tempfile
import threading
from datetime import datetime
import pandas as pd
from werkzeug.utils import secure_filename

# 导入核心功能模块
try:
    from weather_scraper import WeatherScraper
    from update_weather_excel import WeatherExcelUpdater, CITY_MAPPING
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保 weather_scraper.py 和 update_weather_excel.py 文件存在")
    exit(1)

app = Flask(__name__)
app.secret_key = 'weather_scraper_secret_key_2024'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 全局变量
scraper = WeatherScraper()
updater = WeatherExcelUpdater()
current_task = None
task_status = {"status": "idle", "message": "", "progress": 0}

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """主页"""
    return render_template('index.html', cities=list(CITY_MAPPING.keys()))

@app.route('/scrape', methods=['POST'])
def scrape_data():
    """爬取数据API"""
    global current_task, task_status
    
    try:
        data = request.get_json()
        
        # 验证输入
        year = int(data.get('year', datetime.now().year))
        month = int(data.get('month', datetime.now().month))
        cities = data.get('cities', [])
        
        if not cities:
            return jsonify({"error": "请至少选择一个城市"}), 400
        
        if year < 2020 or year > 2025:
            return jsonify({"error": "年份应在2020-2025之间"}), 400
        
        if month < 1 or month > 12:
            return jsonify({"error": "月份应在1-12之间"}), 400
        
        # 检查是否有任务正在运行
        if current_task and current_task.is_alive():
            return jsonify({"error": "已有任务正在运行，请等待完成"}), 400
        
        # 重置任务状态
        task_status = {"status": "running", "message": "开始爬取数据...", "progress": 0}
        
        # 启动爬取任务
        current_task = threading.Thread(target=scrape_task, args=(cities, year, month))
        current_task.start()
        
        return jsonify({"message": "爬取任务已启动"})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

def scrape_task(cities, year, month):
    """爬取任务"""
    global task_status
    
    try:
        all_data = []
        total_cities = len(cities)
        
        for i, chinese_city in enumerate(cities):
            if chinese_city in CITY_MAPPING:
                pinyin_city = CITY_MAPPING[chinese_city]
                task_status["message"] = f"正在爬取 {chinese_city} ({i+1}/{total_cities})..."
                task_status["progress"] = int((i / total_cities) * 100)
                
                city_data = scraper.get_weather_data(pinyin_city, year, month)
                
                # 将拼音城市名转换为中文
                for record in city_data:
                    record['城市'] = chinese_city
                
                all_data.extend(city_data)
        
        if all_data:
            # 生成文件名
            filename = f"天气数据_{year}年{month:02d}月_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            filepath = os.path.join(tempfile.gettempdir(), filename)
            
            task_status["message"] = "正在保存数据..."
            task_status["progress"] = 90
            
            scraper.save_to_excel(all_data, filepath)
            
            task_status["status"] = "completed"
            task_status["message"] = f"成功爬取 {len(all_data)} 条数据"
            task_status["progress"] = 100
            task_status["download_file"] = filepath
            task_status["filename"] = filename
        else:
            task_status["status"] = "error"
            task_status["message"] = "未获取到任何数据"
            
    except Exception as e:
        task_status["status"] = "error"
        task_status["message"] = f"爬取失败: {str(e)}"

@app.route('/status')
def get_status():
    """获取任务状态"""
    return jsonify(task_status)

@app.route('/download/<filename>')
def download_file(filename):
    """下载文件"""
    try:
        filepath = os.path.join(tempfile.gettempdir(), filename)
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True, download_name=filename)
        else:
            return "文件不存在", 404
    except Exception as e:
        return f"下载失败: {str(e)}", 500

@app.route('/update', methods=['GET', 'POST'])
def update_data():
    """更新数据页面"""
    if request.method == 'GET':
        return render_template('update.html')
    
    try:
        # 检查文件上传
        if 'file' not in request.files:
            flash('请选择文件')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('请选择文件')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(tempfile.gettempdir(), filename)
            file.save(filepath)
            
            # 获取表单数据
            year = int(request.form.get('year', datetime.now().year))
            month = int(request.form.get('month', datetime.now().month))
            
            # 验证输入
            if year < 2020 or year > 2025:
                flash('年份应在2020-2025之间')
                return redirect(request.url)
            
            if month < 1 or month > 12:
                flash('月份应在1-12之间')
                return redirect(request.url)
            
            # 检查是否有任务正在运行
            if current_task and current_task.is_alive():
                flash('已有任务正在运行，请等待完成')
                return redirect(request.url)
            
            # 重置任务状态
            task_status["status"] = "running"
            task_status["message"] = "开始更新数据..."
            task_status["progress"] = 0
            
            # 启动更新任务
            current_task = threading.Thread(target=update_task, args=(filepath, year, month))
            current_task.start()
            
            return render_template('update_progress.html')
        else:
            flash('请上传Excel文件(.xlsx或.xls)')
            return redirect(request.url)
            
    except Exception as e:
        flash(f'更新失败: {str(e)}')
        return redirect(request.url)

def update_task(filepath, year, month):
    """更新任务"""
    global task_status
    
    try:
        task_status["message"] = "正在读取文件..."
        task_status["progress"] = 10
        
        # 创建输出文件名
        output_filename = f"更新后的数据_{year}年{month:02d}月_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        output_filepath = os.path.join(tempfile.gettempdir(), output_filename)
        
        task_status["message"] = "正在更新数据..."
        task_status["progress"] = 50
        
        updater.update_excel_data(filepath, year, month, output_filepath)
        
        task_status["status"] = "completed"
        task_status["message"] = "数据更新完成"
        task_status["progress"] = 100
        task_status["download_file"] = output_filepath
        task_status["filename"] = output_filename
        
    except Exception as e:
        task_status["status"] = "error"
        task_status["message"] = f"更新失败: {str(e)}"

@app.route('/help')
def help_page():
    """帮助页面"""
    return render_template('help.html', cities=list(CITY_MAPPING.keys()))

if __name__ == '__main__':
    # 创建templates目录
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("🌤️ 天气数据爬取工具 - Web版本")
    print("启动中...")
    print("请在浏览器中访问: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
