#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有功能的脚本
"""

import os
import pandas as pd
from weather_scraper import WeatherScraper
from update_weather_excel import WeatherExcelUpdater, CITY_MAPPING

def test_basic_scraper():
    """测试基本爬虫功能"""
    print("🧪 测试1: 基本爬虫功能")
    print("-" * 30)
    
    scraper = WeatherScraper()
    
    # 测试爬取单个城市
    print("正在测试爬取嘉兴2024年3月数据...")
    data = scraper.get_weather_data('jiaxing', 2024, 3)
    
    if data:
        print(f"✅ 成功获取 {len(data)} 条数据")
        scraper.save_to_excel(data, 'test_jiaxing.xlsx')
        print("✅ 数据已保存到 test_jiaxing.xlsx")
        
        # 验证数据格式
        df = pd.read_excel('test_jiaxing.xlsx')
        expected_columns = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)']
        if list(df.columns) == expected_columns:
            print("✅ 数据格式正确")
        else:
            print("❌ 数据格式不正确")
            print(f"期望列: {expected_columns}")
            print(f"实际列: {list(df.columns)}")
    else:
        print("❌ 未获取到数据")
    
    print()

def test_multiple_cities():
    """测试多城市爬取"""
    print("🧪 测试2: 多城市爬取功能")
    print("-" * 30)
    
    scraper = WeatherScraper()
    cities = ['jiaxing', 'hangzhou']
    
    print(f"正在测试爬取多城市数据: {cities}")
    scraper.scrape_multiple_cities(cities, 2024, 3, 'test_multiple_cities.xlsx')
    
    # 验证结果
    if os.path.exists('test_multiple_cities.xlsx'):
        df = pd.read_excel('test_multiple_cities.xlsx')
        city_counts = df['城市'].value_counts()
        print(f"✅ 成功爬取多城市数据，总计 {len(df)} 条")
        print("城市数据分布:")
        for city, count in city_counts.items():
            print(f"  {city}: {count} 条")
    else:
        print("❌ 多城市爬取失败")
    
    print()

def test_excel_updater():
    """测试Excel更新功能"""
    print("🧪 测试3: Excel更新功能")
    print("-" * 30)
    
    # 创建一个测试用的Excel文件
    test_data = [
        {'城市': '海宁', '日期': '2024-01-01', '日期类型': '星期一', '白天天气': '晴', '晚上天气': '多云', '最高温度(°C)': 10, '最低温度(°C)': 0},
        {'城市': '杭州', '日期': '2024-01-01', '日期类型': '星期一', '白天天气': '阴', '晚上天气': '雨', '最高温度(°C)': 8, '最低温度(°C)': 2}
    ]
    test_df = pd.DataFrame(test_data)
    test_df.to_excel('test_original.xlsx', index=False)
    print("✅ 创建测试Excel文件")
    
    # 测试更新功能
    updater = WeatherExcelUpdater()
    print("正在测试更新Excel文件...")
    updater.update_excel_file('test_original.xlsx', 2024, 3, 'test_updated.xlsx')
    
    # 验证结果
    if os.path.exists('test_updated.xlsx'):
        df = pd.read_excel('test_updated.xlsx')
        print(f"✅ 更新成功，共 {len(df)} 条数据")
        print(f"城市数量: {df['城市'].nunique()}")
        print(f"日期范围: {df['日期'].min()} 到 {df['日期'].max()}")
    else:
        print("❌ Excel更新失败")
    
    print()

def test_city_mapping():
    """测试城市映射功能"""
    print("🧪 测试4: 城市映射功能")
    print("-" * 30)
    
    print(f"支持的城市数量: {len(CITY_MAPPING)}")
    print("城市映射示例:")
    for i, (chinese, pinyin) in enumerate(list(CITY_MAPPING.items())[:5]):
        print(f"  {chinese} -> {pinyin}")
    
    if len(CITY_MAPPING) > 0:
        print("✅ 城市映射功能正常")
    else:
        print("❌ 城市映射为空")
    
    print()

def test_data_quality():
    """测试数据质量"""
    print("🧪 测试5: 数据质量检查")
    print("-" * 30)
    
    if os.path.exists('test_jiaxing.xlsx'):
        df = pd.read_excel('test_jiaxing.xlsx')
        
        # 检查数据完整性
        missing_data = df.isnull().sum()
        print("缺失数据统计:")
        for col, count in missing_data.items():
            if count > 0:
                print(f"  {col}: {count} 条缺失")
        
        if missing_data.sum() == 0:
            print("✅ 无缺失数据")
        
        # 检查温度数据合理性
        high_temps = df['最高温度(°C)']
        low_temps = df['最低温度(°C)']
        
        if (high_temps >= low_temps).all():
            print("✅ 温度数据逻辑正确（最高温度 >= 最低温度）")
        else:
            print("❌ 发现温度数据异常")
        
        # 检查日期连续性
        dates = pd.to_datetime(df['日期']).sort_values()
        date_diff = dates.diff().dt.days.dropna()
        if (date_diff == 1).all():
            print("✅ 日期数据连续")
        else:
            print("⚠️ 日期数据可能不连续")
    
    print()

def cleanup_test_files():
    """清理测试文件"""
    print("🧹 清理测试文件")
    print("-" * 30)
    
    test_files = [
        'test_jiaxing.xlsx',
        'test_multiple_cities.xlsx', 
        'test_original.xlsx',
        'test_updated.xlsx'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✅ 删除 {file}")
    
    print()

def main():
    """运行所有测试"""
    print("🚀 开始运行功能测试")
    print("=" * 50)
    
    try:
        test_basic_scraper()
        test_multiple_cities()
        test_excel_updater()
        test_city_mapping()
        test_data_quality()
        
        print("🎉 所有测试完成！")
        print("=" * 50)
        
        # 询问是否清理测试文件
        try:
            cleanup = input("是否清理测试文件？(y/n): ").strip().lower()
            if cleanup in ['y', 'yes', '是']:
                cleanup_test_files()
        except (EOFError, KeyboardInterrupt):
            print("\n保留测试文件")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
