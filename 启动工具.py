#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据爬取工具 - 统一启动入口
为用户提供简单的选择界面，可以选择不同的使用方式
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os
import threading
import webbrowser
import time

class ToolLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🌤️ 天气数据爬取工具")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("weather_icon.ico")
        except:
            pass
        
        # 居中显示窗口
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 检查依赖
        self.check_dependencies()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill='both', expand=True)
        
        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        # 大标题
        title_label = ttk.Label(title_frame, text="🌤️ 天气数据爬取工具", 
                               font=('Arial', 18, 'bold'))
        title_label.pack()
        
        # 副标题
        subtitle_label = ttk.Label(title_frame, text="简单易用的天气数据获取工具", 
                                  font=('Arial', 11))
        subtitle_label.pack(pady=(5, 0))
        
        # 版本信息
        version_label = ttk.Label(title_frame, text="版本 1.0.0", 
                                 font=('Arial', 9), foreground='gray')
        version_label.pack(pady=(5, 0))
        
        # 分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.pack(fill='x', pady=10)
        
        # 选择提示
        choose_label = ttk.Label(main_frame, text="请选择使用方式：", 
                                font=('Arial', 12, 'bold'))
        choose_label.pack(pady=(10, 15))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='both', expand=True)
        
        # 图形界面按钮
        gui_frame = ttk.Frame(button_frame)
        gui_frame.pack(fill='x', pady=5)
        
        gui_btn = ttk.Button(gui_frame, text="🖥️ 桌面图形界面", 
                            command=self.start_gui, width=25)
        gui_btn.pack(side='left')
        
        gui_desc = ttk.Label(gui_frame, text="界面友好，适合日常使用", 
                            font=('Arial', 9), foreground='gray')
        gui_desc.pack(side='left', padx=(10, 0))
        
        # 网页版本按钮
        web_frame = ttk.Frame(button_frame)
        web_frame.pack(fill='x', pady=5)
        
        web_btn = ttk.Button(web_frame, text="🌐 网页版本", 
                            command=self.start_web, width=25)
        web_btn.pack(side='left')
        
        web_desc = ttk.Label(web_frame, text="无需安装，支持多人使用", 
                            font=('Arial', 9), foreground='gray')
        web_desc.pack(side='left', padx=(10, 0))
        
        # 命令行版本按钮
        console_frame = ttk.Frame(button_frame)
        console_frame.pack(fill='x', pady=5)
        
        console_btn = ttk.Button(console_frame, text="💻 命令行版本", 
                                command=self.start_console, width=25)
        console_btn.pack(side='left')
        
        console_desc = ttk.Label(console_frame, text="功能完整，支持批量操作", 
                                font=('Arial', 9), foreground='gray')
        console_desc.pack(side='left', padx=(10, 0))
        
        # Chrome插件按钮
        chrome_frame = ttk.Frame(button_frame)
        chrome_frame.pack(fill='x', pady=5)
        
        chrome_btn = ttk.Button(chrome_frame, text="🔌 Chrome插件", 
                               command=self.open_chrome_extension, width=25)
        chrome_btn.pack(side='left')
        
        chrome_desc = ttk.Label(chrome_frame, text="集成在浏览器中使用", 
                               font=('Arial', 9), foreground='gray')
        chrome_desc.pack(side='left', padx=(10, 0))
        
        # 分隔线
        separator2 = ttk.Separator(main_frame, orient='horizontal')
        separator2.pack(fill='x', pady=15)
        
        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill='x')
        
        help_btn = ttk.Button(bottom_frame, text="📖 使用帮助", 
                             command=self.show_help)
        help_btn.pack(side='left')
        
        about_btn = ttk.Button(bottom_frame, text="ℹ️ 关于", 
                              command=self.show_about)
        about_btn.pack(side='left', padx=(10, 0))
        
        exit_btn = ttk.Button(bottom_frame, text="❌ 退出", 
                             command=self.root.quit)
        exit_btn.pack(side='right')
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                font=('Arial', 9), foreground='blue')
        status_label.pack(pady=(10, 0))
    
    def check_dependencies(self):
        """检查依赖包"""
        missing_modules = []
        
        try:
            import requests
        except ImportError:
            missing_modules.append('requests')
        
        try:
            import bs4
        except ImportError:
            missing_modules.append('beautifulsoup4')
        
        try:
            import pandas
        except ImportError:
            missing_modules.append('pandas')
        
        try:
            import openpyxl
        except ImportError:
            missing_modules.append('openpyxl')
        
        if missing_modules:
            self.status_var.set(f"缺少依赖: {', '.join(missing_modules)}")
            messagebox.showwarning(
                "缺少依赖包", 
                f"检测到缺少以下Python包：\n{', '.join(missing_modules)}\n\n"
                f"请运行以下命令安装：\n"
                f"pip install {' '.join(missing_modules)}\n\n"
                f"或者使用打包好的可执行文件版本。"
            )
        else:
            self.status_var.set("所有依赖包已就绪")
    
    def start_gui(self):
        """启动图形界面版本"""
        try:
            self.status_var.set("正在启动图形界面...")
            self.root.update()
            
            from weather_gui import WeatherGUI
            
            # 隐藏启动器窗口
            self.root.withdraw()
            
            # 创建新的GUI窗口
            gui_root = tk.Tk()
            app = WeatherGUI(gui_root)
            gui_root.mainloop()
            
            # GUI关闭后显示启动器
            self.root.deiconify()
            self.status_var.set("图形界面已关闭")
            
        except ImportError as e:
            messagebox.showerror("启动失败", f"无法启动图形界面：\n{str(e)}")
            self.status_var.set("图形界面启动失败")
        except Exception as e:
            messagebox.showerror("错误", f"启动图形界面时发生错误：\n{str(e)}")
            self.status_var.set("发生错误")
    
    def start_web(self):
        """启动网页版本"""
        try:
            self.status_var.set("正在启动网页服务器...")
            self.root.update()
            
            from weather_web import app
            
            # 在新线程中启动Web服务器
            def run_server():
                app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
            
            server_thread = threading.Thread(target=run_server)
            server_thread.daemon = True
            server_thread.start()
            
            # 等待服务器启动
            time.sleep(2)
            
            # 打开浏览器
            webbrowser.open('http://127.0.0.1:5000')
            
            self.status_var.set("网页版本已启动 (http://127.0.0.1:5000)")
            
            messagebox.showinfo(
                "网页版本已启动", 
                "网页版本已在浏览器中打开\n"
                "地址: http://127.0.0.1:5000\n\n"
                "关闭此消息框不会停止服务器\n"
                "如需停止服务器，请关闭启动器程序"
            )
            
        except ImportError as e:
            messagebox.showerror("启动失败", f"无法启动网页版本：\n{str(e)}")
            self.status_var.set("网页版本启动失败")
        except Exception as e:
            messagebox.showerror("错误", f"启动网页版本时发生错误：\n{str(e)}")
            self.status_var.set("发生错误")
    
    def start_console(self):
        """启动命令行版本"""
        try:
            self.status_var.set("正在启动命令行版本...")
            self.root.update()
            
            # 隐藏启动器窗口
            self.root.withdraw()
            
            from 使用指南 import main as console_main
            console_main()
            
            # 命令行版本结束后显示启动器
            self.root.deiconify()
            self.status_var.set("命令行版本已关闭")
            
        except ImportError as e:
            messagebox.showerror("启动失败", f"无法启动命令行版本：\n{str(e)}")
            self.status_var.set("命令行版本启动失败")
        except Exception as e:
            messagebox.showerror("错误", f"启动命令行版本时发生错误：\n{str(e)}")
            self.status_var.set("发生错误")
    
    def open_chrome_extension(self):
        """打开Chrome插件目录"""
        chrome_dir = "chrome_extension"
        if os.path.exists(chrome_dir):
            if sys.platform == "win32":
                os.startfile(chrome_dir)
            elif sys.platform == "darwin":
                os.system(f"open {chrome_dir}")
            else:
                os.system(f"xdg-open {chrome_dir}")
            
            messagebox.showinfo(
                "Chrome插件", 
                "Chrome插件文件夹已打开\n\n"
                "安装步骤：\n"
                "1. 打开Chrome浏览器\n"
                "2. 进入 chrome://extensions/\n"
                "3. 开启"开发者模式"\n"
                "4. 点击"加载已解压的扩展程序"\n"
                "5. 选择chrome_extension文件夹"
            )
        else:
            messagebox.showerror("文件不存在", "未找到Chrome插件文件夹")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
🌤️ 天气数据爬取工具使用帮助

📖 功能介绍：
• 从天气网站爬取历史天气数据
• 支持多个城市批量爬取
• 导出为Excel格式文件
• 提供多种使用方式

🏙️ 支持城市：
海宁、杭州、金华、宁波、衢州、台州、温州、
诸暨、嘉兴、绍兴、湖州、丽水、舟山

📊 数据格式：
城市、日期、日期类型、白天天气、晚上天气、
最高温度、最低温度

⚠️ 注意事项：
• 确保网络连接正常
• 不要频繁爬取数据
• 爬取过程需要耐心等待
• 数据仅供学习和个人使用

🔧 技术支持：
如遇问题请检查网络连接和依赖包安装
        """
        
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("400x500")
        help_window.resizable(False, False)
        
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill='both', expand=True)
        text_widget.insert('1.0', help_text)
        text_widget.config(state='disabled')
        
        # 居中显示帮助窗口
        help_window.update_idletasks()
        x = (help_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (help_window.winfo_screenheight() // 2) - (500 // 2)
        help_window.geometry(f'400x500+{x}+{y}')
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
🌤️ 天气数据爬取工具

版本：1.0.0
开发：天气数据爬取工具团队

这是一个简单易用的天气数据获取工具，
支持从天气网站爬取历史天气数据并导出
为Excel格式。

特点：
✓ 多种使用方式
✓ 界面友好易用
✓ 支持批量操作
✓ 数据格式标准

本工具仅供学习和个人使用，
请遵守相关网站的使用条款。

感谢使用！
        """
        messagebox.showinfo("关于", about_text)
    
    def run(self):
        """运行启动器"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        launcher = ToolLauncher()
        launcher.run()
    except Exception as e:
        messagebox.showerror("启动失败", f"启动器初始化失败：\n{str(e)}")

if __name__ == "__main__":
    main()
