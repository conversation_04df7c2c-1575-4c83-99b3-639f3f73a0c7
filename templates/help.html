{% extends "base.html" %}

{% block title %}帮助 - 天气数据爬取工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0"><i class="bi bi-question-circle"></i> 使用帮助</h4>
            </div>
            <div class="card-body">
                <!-- 功能介绍 -->
                <section class="mb-5">
                    <h5><i class="bi bi-info-circle"></i> 功能介绍</h5>
                    <p>天气数据爬取工具是一个简单易用的网页应用，可以帮助您从天气网站获取历史天气数据并导出为Excel格式。</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="bi bi-download"></i> 爬取新数据
                                    </h6>
                                    <p class="card-text">选择城市和时间，爬取全新的天气数据</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="bi bi-arrow-clockwise"></i> 更新现有数据
                                    </h6>
                                    <p class="card-text">上传Excel文件，更新其中城市的最新数据</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 使用步骤 -->
                <section class="mb-5">
                    <h5><i class="bi bi-list-ol"></i> 使用步骤</h5>
                    
                    <div class="accordion" id="stepsAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="step1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                    步骤1：爬取新数据
                                </button>
                            </h2>
                            <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#stepsAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>在首页选择要爬取的城市（可多选）</li>
                                        <li>设置年份和月份</li>
                                        <li>点击"开始爬取"按钮</li>
                                        <li>等待爬取完成</li>
                                        <li>下载生成的Excel文件</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="step2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                    步骤2：更新现有数据
                                </button>
                            </h2>
                            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#stepsAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>点击"更新数据"标签页</li>
                                        <li>上传现有的Excel文件</li>
                                        <li>设置要更新到的年份和月份</li>
                                        <li>点击"开始更新"按钮</li>
                                        <li>等待更新完成</li>
                                        <li>下载更新后的文件</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 支持的城市 -->
                <section class="mb-5">
                    <h5><i class="bi bi-geo-alt"></i> 支持的城市</h5>
                    <p>目前支持以下城市的天气数据爬取：</p>
                    <div class="row">
                        {% for city in cities %}
                        <div class="col-md-3 col-sm-4 col-6 mb-2">
                            <span class="badge bg-secondary">{{ city }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-lightbulb"></i> 
                        如需添加其他城市支持，请联系开发者。
                    </div>
                </section>

                <!-- 数据格式 -->
                <section class="mb-5">
                    <h5><i class="bi bi-table"></i> 数据格式</h5>
                    <p>生成的Excel文件包含以下列：</p>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>列名</th>
                                    <th>说明</th>
                                    <th>示例</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>城市</td>
                                    <td>城市名称</td>
                                    <td>嘉兴</td>
                                </tr>
                                <tr>
                                    <td>日期</td>
                                    <td>具体日期</td>
                                    <td>2024-03-01</td>
                                </tr>
                                <tr>
                                    <td>日期类型</td>
                                    <td>星期几</td>
                                    <td>星期五</td>
                                </tr>
                                <tr>
                                    <td>白天天气</td>
                                    <td>白天天气状况</td>
                                    <td>晴</td>
                                </tr>
                                <tr>
                                    <td>晚上天气</td>
                                    <td>晚上天气状况</td>
                                    <td>多云</td>
                                </tr>
                                <tr>
                                    <td>最高温度(°C)</td>
                                    <td>当日最高温度</td>
                                    <td>15</td>
                                </tr>
                                <tr>
                                    <td>最低温度(°C)</td>
                                    <td>当日最低温度</td>
                                    <td>3</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 注意事项 -->
                <section class="mb-5">
                    <h5><i class="bi bi-exclamation-triangle"></i> 注意事项</h5>
                    <div class="alert alert-warning">
                        <ul class="mb-0">
                            <li><strong>网络连接：</strong>请确保网络连接正常，爬取过程需要访问天气网站</li>
                            <li><strong>耐心等待：</strong>爬取过程可能需要几分钟时间，请不要关闭浏览器</li>
                            <li><strong>合理使用：</strong>请不要频繁爬取，避免对服务器造成压力</li>
                            <li><strong>数据准确性：</strong>数据来源于第三方网站，准确性以原网站为准</li>
                            <li><strong>文件格式：</strong>更新功能仅支持.xlsx和.xls格式的Excel文件</li>
                        </ul>
                    </div>
                </section>

                <!-- 常见问题 -->
                <section class="mb-5">
                    <h5><i class="bi bi-question-circle"></i> 常见问题</h5>
                    
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse1">
                                    Q: 爬取失败怎么办？
                                </button>
                            </h2>
                            <div id="faqCollapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>如果爬取失败，请检查：</p>
                                    <ul>
                                        <li>网络连接是否正常</li>
                                        <li>选择的年份月份是否合理（建议选择近期日期）</li>
                                        <li>是否选择了支持的城市</li>
                                        <li>稍后重试，可能是网络临时问题</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse2">
                                    Q: 为什么有些城市没有数据？
                                </button>
                            </h2>
                            <div id="faqCollapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>可能的原因：</p>
                                    <ul>
                                        <li>该城市在指定时间段没有数据记录</li>
                                        <li>网站数据更新延迟</li>
                                        <li>城市名称映射问题</li>
                                    </ul>
                                    <p>建议尝试其他时间段或联系开发者。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse3">
                                    Q: 可以爬取多长时间的数据？
                                </button>
                            </h2>
                            <div id="faqCollapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>目前每次只能爬取一个月的数据。如需多个月的数据，请：</p>
                                    <ul>
                                        <li>分别爬取各个月份的数据</li>
                                        <li>使用Excel合并多个文件</li>
                                        <li>或联系开发者添加批量爬取功能</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
{% endblock %}
