{% extends "base.html" %}

{% block title %}更新数据 - 天气数据爬取工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0"><i class="bi bi-arrow-clockwise"></i> 更新现有数据</h4>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <!-- 文件上传 -->
                    <div class="mb-4">
                        <label for="file" class="form-label fw-bold">选择Excel文件</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                        <div class="form-text">请选择要更新的Excel文件（支持.xlsx和.xls格式）</div>
                    </div>

                    <!-- 时间选择 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="year" class="form-label fw-bold">更新到年份</label>
                            <select class="form-select" id="year" name="year" required>
                                {% for year in range(2020, 2026) %}
                                <option value="{{ year }}" {% if year == 2024 %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="month" class="form-label fw-bold">更新到月份</label>
                            <select class="form-select" id="month" name="month" required>
                                {% for month in range(1, 13) %}
                                <option value="{{ month }}" {% if month == 3 %}selected{% endif %}>{{ month }}月</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="bi bi-arrow-clockwise"></i> 开始更新
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> 更新说明</h5>
            </div>
            <div class="card-body">
                <h6>功能说明：</h6>
                <ul>
                    <li>上传现有的Excel文件</li>
                    <li>系统会自动识别文件中的城市</li>
                    <li>爬取指定年月的最新数据</li>
                    <li>生成包含更新数据的新文件</li>
                </ul>
                
                <h6 class="mt-3">注意事项：</h6>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <ul class="mb-0">
                        <li>原文件不会被修改，系统会生成新的文件</li>
                        <li>Excel文件必须包含"城市"列</li>
                        <li>支持的城市：海宁、杭州、金华、宁波、衢州、台州、温州、诸暨、嘉兴、绍兴、湖州、丽水、舟山</li>
                        <li>更新过程可能需要几分钟，请耐心等待</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
