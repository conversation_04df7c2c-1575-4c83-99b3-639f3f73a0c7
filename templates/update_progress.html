{% extends "base.html" %}

{% block title %}更新进度 - 天气数据爬取工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0"><i class="bi bi-arrow-clockwise"></i> 数据更新中</h4>
            </div>
            <div class="card-body text-center">
                <div class="mb-4">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">更新中...</span>
                    </div>
                </div>
                
                <div class="progress mb-3">
                    <div id="progressBar" class="progress-bar bg-success progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%"></div>
                </div>
                
                <div id="statusMessage" class="status-message">正在准备更新...</div>
                
                <!-- 下载链接 -->
                <div id="downloadContainer" class="mt-4" style="display: none;">
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> 更新完成！
                        <div class="mt-2">
                            <a id="downloadLink" href="#" class="btn btn-success">
                                <i class="bi bi-download"></i> 下载更新后的文件
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 错误信息 -->
                <div id="errorContainer" class="mt-4" style="display: none;">
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 更新失败
                        <div id="errorMessage" class="mt-2"></div>
                        <div class="mt-2">
                            <a href="{{ url_for('update_data') }}" class="btn btn-outline-danger">
                                <i class="bi bi-arrow-left"></i> 返回重试
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-body">
                <h6><i class="bi bi-info-circle"></i> 更新过程说明：</h6>
                <ol>
                    <li>读取上传的Excel文件</li>
                    <li>识别文件中的城市列表</li>
                    <li>逐个爬取各城市的最新数据</li>
                    <li>合并数据并生成新文件</li>
                </ol>
                <div class="alert alert-info mt-3">
                    <i class="bi bi-clock"></i> 
                    <strong>请耐心等待：</strong>更新过程可能需要几分钟时间，请不要关闭此页面。
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let statusCheckInterval;

// 页面加载后开始检查状态
document.addEventListener('DOMContentLoaded', function() {
    statusCheckInterval = setInterval(checkStatus, 1000);
});

function checkStatus() {
    fetch('/status')
    .then(response => response.json())
    .then(data => {
        const progressBar = document.getElementById('progressBar');
        const statusMessage = document.getElementById('statusMessage');
        
        progressBar.style.width = data.progress + '%';
        statusMessage.textContent = data.message;
        
        if (data.status === 'completed') {
            clearInterval(statusCheckInterval);
            
            // 显示下载链接
            const downloadLink = document.getElementById('downloadLink');
            downloadLink.href = '/download/' + data.filename;
            document.getElementById('downloadContainer').style.display = 'block';
            
        } else if (data.status === 'error') {
            clearInterval(statusCheckInterval);
            
            // 显示错误信息
            document.getElementById('errorMessage').textContent = data.message;
            document.getElementById('errorContainer').style.display = 'block';
        }
    })
    .catch(error => {
        console.error('状态检查失败:', error);
        clearInterval(statusCheckInterval);
        
        document.getElementById('errorMessage').textContent = '无法获取更新状态，请刷新页面重试';
        document.getElementById('errorContainer').style.display = 'block';
    });
}
</script>
{% endblock %}
