{% extends "base.html" %}

{% block title %}爬取天气数据 - 天气数据爬取工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="bi bi-download"></i> 爬取天气数据</h4>
            </div>
            <div class="card-body">
                <form id="scrapeForm">
                    <!-- 城市选择 -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">选择城市</label>
                        <div class="row">
                            {% for city in cities %}
                            <div class="col-md-3 col-sm-4 col-6">
                                <div class="form-check city-checkbox">
                                    <input class="form-check-input" type="checkbox" value="{{ city }}" id="city_{{ loop.index }}">
                                    <label class="form-check-label" for="city_{{ loop.index }}">
                                        {{ city }}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllCities()">全选</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllCities()">清空</button>
                        </div>
                    </div>

                    <!-- 时间选择 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="year" class="form-label fw-bold">年份</label>
                            <select class="form-select" id="year" required>
                                {% for year in range(2020, 2026) %}
                                <option value="{{ year }}" {% if year == 2024 %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="month" class="form-label fw-bold">月份</label>
                            <select class="form-select" id="month" required>
                                {% for month in range(1, 13) %}
                                <option value="{{ month }}" {% if month == 3 %}selected{% endif %}>{{ month }}月</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="scrapeBtn">
                            <i class="bi bi-download"></i> 开始爬取
                        </button>
                    </div>
                </form>

                <!-- 进度显示 -->
                <div id="progressContainer" class="progress-container" style="display: none;">
                    <div class="progress">
                        <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="statusMessage" class="status-message text-center"></div>
                </div>

                <!-- 下载链接 -->
                <div id="downloadContainer" class="mt-3" style="display: none;">
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> 爬取完成！
                        <a id="downloadLink" href="#" class="btn btn-success ms-2">
                            <i class="bi bi-download"></i> 下载Excel文件
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> 使用说明</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>选择要爬取的城市（可以选择多个）</li>
                    <li>设置年份和月份</li>
                    <li>点击"开始爬取"按钮</li>
                    <li>等待爬取完成后下载Excel文件</li>
                </ol>
                <div class="alert alert-info mt-3">
                    <i class="bi bi-lightbulb"></i> 
                    <strong>提示：</strong>爬取过程可能需要几分钟时间，请耐心等待。生成的Excel文件包含城市、日期、天气、温度等详细信息。
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let statusCheckInterval;

document.getElementById('scrapeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // 获取选中的城市
    const selectedCities = [];
    document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
        selectedCities.push(checkbox.value);
    });
    
    if (selectedCities.length === 0) {
        alert('请至少选择一个城市');
        return;
    }
    
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;
    
    // 发送爬取请求
    fetch('/scrape', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            cities: selectedCities,
            year: parseInt(year),
            month: parseInt(month)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('错误: ' + data.error);
        } else {
            // 显示进度条
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('downloadContainer').style.display = 'none';
            document.getElementById('scrapeBtn').disabled = true;
            
            // 开始检查状态
            statusCheckInterval = setInterval(checkStatus, 1000);
        }
    })
    .catch(error => {
        alert('请求失败: ' + error);
    });
});

function checkStatus() {
    fetch('/status')
    .then(response => response.json())
    .then(data => {
        const progressBar = document.getElementById('progressBar');
        const statusMessage = document.getElementById('statusMessage');
        
        progressBar.style.width = data.progress + '%';
        statusMessage.textContent = data.message;
        
        if (data.status === 'completed') {
            clearInterval(statusCheckInterval);
            document.getElementById('scrapeBtn').disabled = false;
            
            // 显示下载链接
            const downloadLink = document.getElementById('downloadLink');
            downloadLink.href = '/download/' + data.filename;
            document.getElementById('downloadContainer').style.display = 'block';
            
        } else if (data.status === 'error') {
            clearInterval(statusCheckInterval);
            document.getElementById('scrapeBtn').disabled = false;
            alert('爬取失败: ' + data.message);
            document.getElementById('progressContainer').style.display = 'none';
        }
    })
    .catch(error => {
        console.error('状态检查失败:', error);
    });
}

function selectAllCities() {
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function clearAllCities() {
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
}
</script>
{% endblock %}
