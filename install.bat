@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🌤️ 天气数据爬取工具 - 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo.
    echo 请先安装Python 3.7或更高版本：
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✓ Python环境检查通过
echo.

echo 正在检查pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到pip
    echo 请确保pip已正确安装
    pause
    exit /b 1
)

echo ✓ pip检查通过
echo.

echo 正在安装依赖包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ 依赖安装失败
    echo.
    echo 可能的解决方案：
    echo 1. 检查网络连接
    echo 2. 尝试使用国内镜像源：
    echo    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    echo 3. 手动安装各个包：
    echo    pip install requests beautifulsoup4 pandas openpyxl flask
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 依赖安装完成！
echo.
echo 🚀 现在可以使用天气数据爬取工具了：
echo.
echo 启动方式：
echo 1. 双击运行 "启动工具.py" （推荐）
echo 2. 或者在命令行中运行：python 启动工具.py
echo.
echo 其他使用方式：
echo • 图形界面：python weather_gui.py
echo • 网页版本：python weather_web.py
echo • 命令行版：python 使用指南.py
echo.
echo 📖 详细使用说明请查看 "用户使用指南.md"
echo.
pause
