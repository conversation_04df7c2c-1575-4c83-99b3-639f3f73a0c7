#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将天气爬取工具打包成可执行文件
使用PyInstaller将Python程序打包成exe文件，方便没有Python环境的用户使用
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✓ PyInstaller 已安装")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("正在安装 PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller 安装失败")
            return False

def create_main_script():
    """创建主启动脚本"""
    main_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
'''
天气数据爬取工具 - 主启动程序
'''

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    '''主函数'''
    try:
        # 检查依赖
        missing_modules = []
        
        try:
            import requests
        except ImportError:
            missing_modules.append('requests')
            
        try:
            import beautifulsoup4
        except ImportError:
            missing_modules.append('beautifulsoup4')
            
        try:
            import pandas
        except ImportError:
            missing_modules.append('pandas')
            
        try:
            import openpyxl
        except ImportError:
            missing_modules.append('openpyxl')
        
        if missing_modules:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror(
                "缺少依赖", 
                f"缺少以下Python模块：\\n{', '.join(missing_modules)}\\n\\n请安装这些模块后重试。"
            )
            return
        
        # 显示选择界面
        root = tk.Tk()
        root.title("天气数据爬取工具")
        root.geometry("400x300")
        root.resizable(False, False)
        
        # 居中显示
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (400 // 2)
        y = (root.winfo_screenheight() // 2) - (300 // 2)
        root.geometry(f"400x300+{x}+{y}")
        
        # 标题
        title_label = tk.Label(root, text="🌤️ 天气数据爬取工具", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        subtitle_label = tk.Label(root, text="请选择使用方式", 
                                 font=('Arial', 12))
        subtitle_label.pack(pady=10)
        
        # 按钮框架
        button_frame = tk.Frame(root)
        button_frame.pack(pady=20)
        
        def start_gui():
            root.destroy()
            from weather_gui import main as gui_main
            gui_main()
        
        def start_console():
            root.destroy()
            from 使用指南 import main as console_main
            console_main()
        
        def start_web():
            root.destroy()
            import webbrowser
            import threading
            from weather_web import app
            
            # 在新线程中启动Web服务器
            def run_server():
                app.run(debug=False, host='127.0.0.1', port=5000)
            
            server_thread = threading.Thread(target=run_server)
            server_thread.daemon = True
            server_thread.start()
            
            # 等待服务器启动
            import time
            time.sleep(2)
            
            # 打开浏览器
            webbrowser.open('http://127.0.0.1:5000')
            
            # 显示提示
            messagebox.showinfo("Web版本已启动", 
                              "Web版本已在浏览器中打开\\n地址: http://127.0.0.1:5000\\n\\n关闭此消息框不会停止服务器")
        
        # 图形界面按钮
        gui_btn = tk.Button(button_frame, text="🖥️ 图形界面版本", 
                           command=start_gui, width=20, height=2,
                           font=('Arial', 10))
        gui_btn.pack(pady=5)
        
        # 命令行界面按钮
        console_btn = tk.Button(button_frame, text="💻 命令行版本", 
                               command=start_console, width=20, height=2,
                               font=('Arial', 10))
        console_btn.pack(pady=5)
        
        # Web界面按钮
        web_btn = tk.Button(button_frame, text="🌐 网页版本", 
                           command=start_web, width=20, height=2,
                           font=('Arial', 10))
        web_btn.pack(pady=5)
        
        # 说明文本
        info_label = tk.Label(root, 
                             text="图形界面：适合日常使用\\n命令行：适合批量操作\\n网页版：适合多人使用", 
                             font=('Arial', 9), fg='gray')
        info_label.pack(pady=20)
        
        root.mainloop()
        
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", f"程序启动失败：\\n{str(e)}")

if __name__ == "__main__":
    main()
"""
    
    with open("weather_main.py", "w", encoding="utf-8") as f:
        f.write(main_script)
    
    print("✓ 主启动脚本创建完成")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # Windows下不显示控制台窗口
        "--name=天气数据爬取工具",  # 可执行文件名称
        "--icon=weather_icon.ico",  # 图标文件（如果存在）
        "--add-data=templates;templates",  # 包含模板文件
        "--hidden-import=requests",
        "--hidden-import=beautifulsoup4", 
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=flask",
        "weather_main.py"
    ]
    
    try:
        # 如果没有图标文件，移除图标参数
        if not os.path.exists("weather_icon.ico"):
            cmd = [arg for arg in cmd if not arg.startswith("--icon")]
        
        # 如果没有templates目录，移除模板参数
        if not os.path.exists("templates"):
            cmd = [arg for arg in cmd if not arg.startswith("--add-data")]
        
        subprocess.check_call(cmd)
        print("✓ 可执行文件构建成功")
        
        # 检查输出文件
        dist_dir = Path("dist")
        if dist_dir.exists():
            exe_files = list(dist_dir.glob("*.exe"))
            if exe_files:
                print(f"✓ 可执行文件位置: {exe_files[0]}")
            else:
                print("⚠️ 未找到生成的exe文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def create_installer_script():
    """创建安装脚本"""
    installer_script = """@echo off
echo 天气数据爬取工具 - 依赖安装脚本
echo ================================

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo Python环境检查通过

echo 正在安装依赖包...
pip install requests beautifulsoup4 pandas openpyxl flask

if errorlevel 1 (
    echo 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)

echo 依赖安装完成！
echo 现在可以运行天气数据爬取工具了
pause
"""
    
    with open("install_dependencies.bat", "w", encoding="gbk") as f:
        f.write(installer_script)
    
    print("✓ 依赖安装脚本创建完成")

def main():
    """主函数"""
    print("🌤️ 天气数据爬取工具 - 可执行文件构建器")
    print("=" * 50)
    
    # 检查当前目录是否包含必要文件
    required_files = ["weather_scraper.py", "update_weather_excel.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("请确保在项目根目录运行此脚本")
        return
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        return
    
    # 创建主启动脚本
    create_main_script()
    
    # 创建依赖安装脚本
    create_installer_script()
    
    # 构建可执行文件
    if build_executable():
        print("\n🎉 构建完成！")
        print("\n使用说明:")
        print("1. 可执行文件位于 dist/ 目录中")
        print("2. 如果用户没有Python环境，请提供 install_dependencies.bat 脚本")
        print("3. 用户需要先运行安装脚本，然后运行可执行文件")
        
        # 清理临时文件
        cleanup_files = ["weather_main.py", "weather_main.spec"]
        for file in cleanup_files:
            if os.path.exists(file):
                os.remove(file)
                
        # 清理临时目录
        cleanup_dirs = ["build", "__pycache__"]
        for dir_name in cleanup_dirs:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
        
        print("4. 临时文件已清理")
    else:
        print("❌ 构建失败")

if __name__ == "__main__":
    main()
