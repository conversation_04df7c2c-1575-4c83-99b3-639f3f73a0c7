#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气爬虫使用示例
"""

from weather_scraper import WeatherScraper
import pandas as pd

def main():
    scraper = WeatherScraper()
    
    # 示例1: 爬取单个城市数据
    print("=== 示例1: 爬取嘉兴2024年3月天气数据 ===")
    jiaxing_data = scraper.get_weather_data('jiaxing', 2024, 3)
    scraper.save_to_excel(jiaxing_data, '嘉兴2024年03月天气.xlsx')
    
    # 示例2: 爬取多个城市数据
    print("\n=== 示例2: 爬取多个城市2024年3月天气数据 ===")
    cities = ['jiaxing', 'hangzhou', 'ningbo', 'wenzhou', 'shaoxing']
    scraper.scrape_multiple_cities(cities, 2024, 3, '浙江多城市2024年03月天气.xlsx')
    
    # 示例3: 爬取多个月份数据
    print("\n=== 示例3: 爬取嘉兴2024年1-3月天气数据 ===")
    all_months_data = []
    for month in range(1, 4):  # 1月到3月
        print(f"正在爬取{month}月数据...")
        month_data = scraper.get_weather_data('jiaxing', 2024, month)
        all_months_data.extend(month_data)
    
    scraper.save_to_excel(all_months_data, '嘉兴2024年1-3月天气.xlsx')
    
    print("\n=== 所有示例完成 ===")

if __name__ == "__main__":
    main()
