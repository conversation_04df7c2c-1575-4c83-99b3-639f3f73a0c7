# 🌤️ 天气数据爬取工具 - 用户使用指南

## 📋 概述

天气数据爬取工具是一个专为普通用户设计的简单易用工具，可以帮助您从天气网站获取历史天气数据并导出为Excel格式。我们提供了多种使用方式，满足不同用户的需求。

## 🚀 快速开始

### 方式一：桌面图形界面（推荐新手）

**特点：** 界面友好，操作简单，适合日常使用

**使用步骤：**
1. 双击运行 `weather_gui.py` 或打包后的exe文件
2. 在界面中选择要爬取的城市
3. 设置年份和月份
4. 点击"开始爬取"按钮
5. 等待完成后保存Excel文件

**界面说明：**
- **爬取新数据标签页：** 选择城市和时间，爬取全新数据
- **更新现有文件标签页：** 上传Excel文件，更新其中的数据
- **帮助标签页：** 查看详细使用说明

### 方式二：网页版本（推荐多人使用）

**特点：** 无需安装，通过浏览器使用，支持多人同时访问

**使用步骤：**
1. 运行 `python weather_web.py`
2. 在浏览器中访问 `http://localhost:5000`
3. 在网页中选择城市和时间
4. 点击"开始爬取"等待完成
5. 下载生成的Excel文件

**网页功能：**
- **爬取数据页面：** 选择城市和时间进行爬取
- **更新数据页面：** 上传Excel文件进行数据更新
- **帮助页面：** 查看详细使用说明

### 方式三：Chrome浏览器插件

**特点：** 集成在浏览器中，随时可用

**安装步骤：**
1. 打开Chrome浏览器
2. 进入扩展程序管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome_extension` 文件夹

**使用步骤：**
1. 点击浏览器工具栏中的插件图标
2. 在弹出窗口中选择城市和时间
3. 点击"开始爬取"
4. 等待完成后下载文件

### 方式四：命令行版本（推荐批量操作）

**特点：** 功能完整，支持批量操作和自动化

**使用步骤：**
1. 运行 `python 使用指南.py`
2. 根据菜单选择相应功能
3. 按提示输入参数
4. 等待处理完成

## 🏙️ 支持的城市

目前支持以下城市的天气数据爬取：

| 城市 | 城市 | 城市 | 城市 |
|------|------|------|------|
| 海宁 | 杭州 | 金华 | 宁波 |
| 衢州 | 台州 | 温州 | 诸暨 |
| 嘉兴 | 绍兴 | 湖州 | 丽水 |
| 舟山 | | | |

## 📊 数据格式

生成的Excel文件包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 城市 | 城市名称 | 嘉兴 |
| 日期 | 具体日期 | 2024-03-01 |
| 日期类型 | 星期几 | 星期五 |
| 白天天气 | 白天天气状况 | 晴 |
| 晚上天气 | 晚上天气状况 | 多云 |
| 最高温度(°C) | 当日最高温度 | 15 |
| 最低温度(°C) | 当日最低温度 | 3 |

## 💻 环境要求

### Python环境（如果使用源码）
- Python 3.7 或更高版本
- 依赖包：requests, beautifulsoup4, pandas, openpyxl, flask, tkinter

### 安装依赖
```bash
pip install requests beautifulsoup4 pandas openpyxl flask
```

### 系统要求
- **Windows:** Windows 7 或更高版本
- **macOS:** macOS 10.12 或更高版本  
- **Linux:** 支持主流发行版
- **浏览器:** Chrome 88+ (插件版本)

## 🔧 安装方式

### 方式一：使用源码（推荐开发者）
1. 下载项目文件到本地
2. 安装Python依赖包
3. 运行相应的Python脚本

### 方式二：使用可执行文件（推荐普通用户）
1. 下载打包好的exe文件
2. 双击运行即可使用
3. 无需安装Python环境

### 方式三：使用浏览器插件
1. 下载chrome_extension文件夹
2. 在Chrome中加载插件
3. 点击插件图标使用

## ⚠️ 注意事项

### 使用建议
- **网络连接：** 确保网络连接稳定
- **合理使用：** 不要频繁爬取，避免对服务器造成压力
- **耐心等待：** 爬取过程可能需要几分钟时间
- **数据备份：** 重要数据请及时备份

### 常见问题

**Q: 爬取失败怎么办？**
A: 检查网络连接，确认选择的城市和时间是否正确，稍后重试。

**Q: 为什么有些城市没有数据？**
A: 可能是该城市在指定时间段没有数据记录，或网站数据更新延迟。

**Q: 可以爬取多长时间的数据？**
A: 目前每次只能爬取一个月的数据，如需多个月数据请分别爬取。

**Q: 生成的文件在哪里？**
A: 默认保存在程序运行目录，也可以在界面中选择保存位置。

## 🆘 技术支持

### 错误排查
1. **导入错误：** 检查是否安装了所有依赖包
2. **网络错误：** 检查网络连接和防火墙设置
3. **文件错误：** 确认有足够的磁盘空间和写入权限
4. **数据错误：** 检查选择的城市和时间是否合理

### 获取帮助
- 查看程序内置的帮助文档
- 检查错误日志信息
- 尝试使用不同的使用方式
- 联系技术支持

## 📈 高级功能

### 批量操作
- 使用命令行版本可以批量处理多个城市
- 支持批量更新现有Excel文件
- 可以通过脚本实现自动化操作

### 数据处理
- 支持合并多个Excel文件
- 可以筛选特定日期范围的数据
- 支持导出不同格式的文件

### 自定义设置
- 可以修改城市映射表添加新城市
- 支持自定义输出格式
- 可以调整爬取间隔时间

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 支持13个城市的天气数据爬取
- ✅ 提供4种不同的使用方式
- ✅ 完整的图形界面和网页界面
- ✅ Chrome浏览器插件支持
- ✅ 可执行文件打包功能
- ✅ 详细的使用文档和帮助

## 📝 许可证

本工具仅供学习和个人使用，请遵守相关网站的使用条款，不要用于商业用途或大规模数据采集。

---

**感谢使用天气数据爬取工具！** 🌤️

如有问题或建议，欢迎反馈。
