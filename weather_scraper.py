#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气网站数据爬取工具
支持从 https://www.tianqi24.com 爬取历史天气数据
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import re
from datetime import datetime
import time
import random
from urllib.parse import urljoin
import argparse
import sys


class WeatherScraper:
    def __init__(self):
        self.base_url = "https://www.tianqi24.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_weather_data(self, city, year, month):
        """
        获取指定城市、年份、月份的天气数据
        
        Args:
            city (str): 城市名称（拼音）
            year (int): 年份
            month (int): 月份
            
        Returns:
            list: 天气数据列表
        """
        # 构建URL
        url = f"{self.base_url}/{city}/history{year}{month:02d}.html"
        
        try:
            print(f"正在爬取: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找天气数据表格
            weather_data = []
            
            # 查找包含天气数据的列表
            weather_list = soup.find_all('li')
            
            for li in weather_list:
                # 查找日期信息
                date_text = li.get_text(strip=True)
                
                # 使用正则表达式匹配日期格式 (如: 03-01)
                date_match = re.search(r'(\d{2})-(\d{2})', date_text)
                if not date_match:
                    continue
                
                month_day = date_match.group(0)
                full_date = f"{year}-{month_day}"
                
                # 解析天气信息
                weather_info = self._parse_weather_info(li, full_date, city)
                if weather_info:
                    weather_data.append(weather_info)
            
            # 如果上面的方法没有找到数据，尝试其他解析方法
            if not weather_data:
                weather_data = self._parse_alternative_format(soup, city, year, month)
            
            print(f"成功获取 {len(weather_data)} 条天气数据")
            return weather_data
            
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return []
        except Exception as e:
            print(f"解析失败: {e}")
            return []
    
    def _parse_weather_info(self, li_element, date, city):
        """解析单个天气信息"""
        try:
            text = li_element.get_text(strip=True)

            # 提取温度信息 - 改进正则表达式
            temp_pattern = r'(\d+)℃(-?\d+)℃'
            temp_match = re.search(temp_pattern, text)

            if not temp_match:
                return None

            high_temp = int(temp_match.group(1))
            low_temp = int(temp_match.group(2))

            # 提取天气信息 - 改进解析逻辑
            # 格式通常是：日期 + 白天天气 + / + 晚上天气 + 温度...
            # 例如：03-01晴/ 多云10℃-2℃...

            # 先移除日期部分
            weather_part = re.sub(r'^\d{2}-\d{2}', '', text)

            # 查找 / 分隔符来区分白天和晚上天气
            if '/' in weather_part:
                parts = weather_part.split('/')
                day_part = parts[0].strip()
                night_part = parts[1].split('℃')[0].strip()  # 取温度前的部分

                # 提取白天天气
                day_weather_match = re.search(r'(晴|多云|阴|雨|小雨|中雨|大雨|暴雨|雪|雾|霾|沙)', day_part)
                day_weather = day_weather_match.group(1) if day_weather_match else "未知"

                # 提取晚上天气
                night_weather_match = re.search(r'(晴|多云|阴|雨|小雨|中雨|大雨|暴雨|雪|雾|霾|沙)', night_part)
                night_weather = night_weather_match.group(1) if night_weather_match else day_weather
            else:
                # 没有/分隔符，只有一种天气
                weather_match = re.search(r'(晴|多云|阴|雨|小雨|中雨|大雨|暴雨|雪|雾|霾|沙)', weather_part)
                day_weather = weather_match.group(1) if weather_match else "未知"
                night_weather = day_weather

            # 获取星期信息
            weekday = self._get_weekday(date)

            return {
                '城市': city,
                '日期': date,
                '日期类型': weekday,
                '白天天气': day_weather,
                '晚上天气': night_weather,
                '最高温度(°C)': high_temp,
                '最低温度(°C)': low_temp
            }

        except Exception as e:
            print(f"解析天气信息失败: {e}")
            return None
    
    def _parse_alternative_format(self, soup, city, year, month):
        """备用解析方法"""
        weather_data = []
        
        # 查找所有可能包含天气数据的元素
        for element in soup.find_all(['tr', 'div', 'li']):
            text = element.get_text(strip=True)
            
            # 查找日期模式
            date_pattern = rf'{month:02d}-(\d{{2}})'
            date_match = re.search(date_pattern, text)
            
            if date_match:
                day = date_match.group(1)
                full_date = f"{year}-{month:02d}-{day}"
                
                # 提取温度
                temp_pattern = r'(\d+)℃.*?(-?\d+)℃'
                temp_match = re.search(temp_pattern, text)
                
                if temp_match:
                    high_temp = int(temp_match.group(1))
                    low_temp = int(temp_match.group(2))
                    
                    # 提取天气
                    weather_pattern = r'(晴|多云|阴|雨|小雨|中雨|大雨|暴雨|雪|雾|霾|沙)'
                    weather_matches = re.findall(weather_pattern, text)
                    
                    day_weather = weather_matches[0] if weather_matches else "未知"
                    night_weather = weather_matches[1] if len(weather_matches) > 1 else day_weather
                    
                    weekday = self._get_weekday(full_date)
                    
                    weather_data.append({
                        '城市': city,
                        '日期': full_date,
                        '日期类型': weekday,
                        '白天天气': day_weather,
                        '晚上天气': night_weather,
                        '最高温度(°C)': high_temp,
                        '最低温度(°C)': low_temp
                    })
        
        return weather_data
    
    def _get_weekday(self, date_str):
        """获取日期对应的星期"""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
            return weekdays[date_obj.weekday()]
        except:
            return "未知"
    
    def save_to_excel(self, data, filename):
        """保存数据到Excel文件"""
        if not data:
            print("没有数据可保存")
            return
        
        df = pd.DataFrame(data)
        
        # 确保列的顺序正确
        columns_order = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)']
        df = df.reindex(columns=columns_order)
        
        # 保存到Excel
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"数据已保存到: {filename}")
    
    def scrape_multiple_cities(self, cities, year, month, output_file):
        """爬取多个城市的天气数据"""
        all_data = []
        
        for city in cities:
            print(f"\n正在处理城市: {city}")
            city_data = self.get_weather_data(city, year, month)
            all_data.extend(city_data)
            
            # 添加随机延迟，避免请求过于频繁
            time.sleep(random.uniform(1, 3))
        
        if all_data:
            self.save_to_excel(all_data, output_file)
        else:
            print("未获取到任何数据")


def main():
    parser = argparse.ArgumentParser(description='天气数据爬取工具')
    parser.add_argument('--city', type=str, default='jiaxing', help='城市名称（拼音）')
    parser.add_argument('--cities', type=str, nargs='+', help='多个城市名称（拼音）')
    parser.add_argument('--year', type=int, default=2024, help='年份')
    parser.add_argument('--month', type=int, default=3, help='月份')
    parser.add_argument('--output', type=str, default='weather_data.xlsx', help='输出文件名')
    
    args = parser.parse_args()
    
    scraper = WeatherScraper()
    
    if args.cities:
        # 爬取多个城市
        scraper.scrape_multiple_cities(args.cities, args.year, args.month, args.output)
    else:
        # 爬取单个城市
        data = scraper.get_weather_data(args.city, args.year, args.month)
        scraper.save_to_excel(data, args.output)


if __name__ == "__main__":
    main()
