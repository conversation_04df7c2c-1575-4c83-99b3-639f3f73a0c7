#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气爬虫工具使用指南和示例
"""

import os
from weather_scraper import WeatherScraper
from update_weather_excel import WeatherExcelUpdater

def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🌤️  天气数据爬取工具")
    print("="*50)
    print("1. 爬取单个城市天气数据")
    print("2. 爬取多个城市天气数据")
    print("3. 更新现有Excel文件")
    print("4. 向Excel文件添加新城市")
    print("5. 查看支持的城市列表")
    print("6. 批量爬取多个月份数据")
    print("7. 预览和验证Excel文件")
    print("8. 测试网络连接")
    print("0. 退出")
    print("="*50)

def get_supported_cities():
    """获取支持的城市列表"""
    from update_weather_excel import CITY_MAPPING
    return CITY_MAPPING

def option_1():
    """爬取单个城市天气数据"""
    print("\n📍 爬取单个城市天气数据")
    print("支持的城市示例: jiaxing(嘉兴), hangzhou(杭州), shanghai(上海), beijing(北京)")

    try:
        city = input("请输入城市名称（拼音，如jiaxing）: ").strip()
        if not city:
            print("❌ 城市名称不能为空")
            return

        year = int(input("请输入年份（如2024）: "))
        if year < 2000 or year > 2025:
            print("❌ 年份应在2000-2025之间")
            return

        month = int(input("请输入月份（1-12）: "))
        if month < 1 or month > 12:
            print("❌ 月份应在1-12之间")
            return

        output = input("请输入输出文件名（如天气数据.xlsx）: ").strip()
        if not output:
            output = f"{city}_{year}年{month:02d}月天气.xlsx"
            print(f"使用默认文件名: {output}")

        if not output.endswith('.xlsx'):
            output += '.xlsx'

        print(f"\n开始爬取 {city} {year}年{month}月的天气数据...")
        scraper = WeatherScraper()
        data = scraper.get_weather_data(city, year, month)

        if data:
            scraper.save_to_excel(data, output)
            print(f"✅ 成功保存 {len(data)} 条数据到 {output}")
        else:
            print("❌ 未获取到数据，请检查城市名称和日期是否正确")

    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 操作失败: {e}")

def option_2():
    """爬取多个城市天气数据"""
    print("\n🏙️ 爬取多个城市天气数据")
    print("示例: jiaxing hangzhou ningbo shaoxing")

    try:
        cities_input = input("请输入城市名称（拼音，用空格分隔）: ").strip()
        if not cities_input:
            print("❌ 请输入至少一个城市名称")
            return

        cities = cities_input.split()
        print(f"将爬取以下城市: {', '.join(cities)}")

        year = int(input("请输入年份（如2024）: "))
        if year < 2000 or year > 2025:
            print("❌ 年份应在2000-2025之间")
            return

        month = int(input("请输入月份（1-12）: "))
        if month < 1 or month > 12:
            print("❌ 月份应在1-12之间")
            return

        output = input("请输入输出文件名（如多城市天气.xlsx）: ").strip()
        if not output:
            output = f"多城市_{year}年{month:02d}月天气.xlsx"
            print(f"使用默认文件名: {output}")

        if not output.endswith('.xlsx'):
            output += '.xlsx'

        print(f"\n开始爬取 {len(cities)} 个城市的天气数据...")
        scraper = WeatherScraper()
        scraper.scrape_multiple_cities(cities, year, month, output)

    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 操作失败: {e}")

def option_3():
    """更新现有Excel文件"""
    print("\n📊 更新现有Excel文件")
    print("此功能会读取现有Excel文件中的城市列表，并更新为指定年月的数据")

    try:
        excel_file = input("请输入Excel文件路径（如八月地方天气.xlsx）: ").strip()
        if not excel_file:
            print("❌ 请输入文件路径")
            return

        if not os.path.exists(excel_file):
            print(f"❌ 文件 {excel_file} 不存在！")
            print("请确认文件路径是否正确")
            return

        # 显示文件信息
        try:
            import pandas as pd
            df = pd.read_excel(excel_file)
            cities = df['城市'].unique()
            print(f"📋 文件中包含的城市: {', '.join(cities)}")
            print(f"📊 当前数据量: {len(df)} 条")
        except Exception as e:
            print(f"⚠️ 无法读取文件信息: {e}")

        year = int(input("请输入年份（如2024）: "))
        if year < 2000 or year > 2025:
            print("❌ 年份应在2000-2025之间")
            return

        month = int(input("请输入月份（1-12）: "))
        if month < 1 or month > 12:
            print("❌ 月份应在1-12之间")
            return

        output = input("请输入输出文件名（回车覆盖原文件）: ").strip()
        if not output:
            confirm = input(f"⚠️ 将覆盖原文件 {excel_file}，确认吗？(y/n): ").strip().lower()
            if confirm not in ['y', 'yes', '是']:
                print("操作已取消")
                return
            output = None
        elif not output.endswith('.xlsx'):
            output += '.xlsx'

        print(f"\n开始更新Excel文件...")
        updater = WeatherExcelUpdater()
        updater.update_excel_file(excel_file, year, month, output)

    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 操作失败: {e}")

def option_4():
    """向Excel文件添加新城市"""
    print("\n➕ 向Excel文件添加新城市")
    print("此功能会在现有Excel文件基础上添加新城市的数据")

    try:
        excel_file = input("请输入Excel文件路径（如八月地方天气.xlsx）: ").strip()
        if not excel_file:
            print("❌ 请输入文件路径")
            return

        if not os.path.exists(excel_file):
            print(f"❌ 文件 {excel_file} 不存在！")
            return

        # 显示当前文件信息
        try:
            import pandas as pd
            df = pd.read_excel(excel_file)
            existing_cities = df['城市'].unique()
            print(f"📋 文件中现有城市: {', '.join(existing_cities)}")
        except Exception as e:
            print(f"⚠️ 无法读取文件信息: {e}")

        # 显示支持的城市
        cities_mapping = get_supported_cities()
        print(f"🗺️ 支持添加的城市: {', '.join(cities_mapping.keys())}")

        cities_input = input("请输入要添加的城市名称（中文，用空格分隔，如: 嘉兴 绍兴）: ").strip()
        if not cities_input:
            print("❌ 请输入至少一个城市名称")
            return

        new_cities = cities_input.split()
        print(f"将添加以下城市: {', '.join(new_cities)}")

        # 检查城市是否支持
        unsupported = [city for city in new_cities if city not in cities_mapping]
        if unsupported:
            print(f"⚠️ 以下城市暂不支持: {', '.join(unsupported)}")
            confirm = input("是否继续添加支持的城市？(y/n): ").strip().lower()
            if confirm not in ['y', 'yes', '是']:
                return
            new_cities = [city for city in new_cities if city in cities_mapping]

        if not new_cities:
            print("❌ 没有可添加的城市")
            return

        year = int(input("请输入年份（如2024）: "))
        if year < 2000 or year > 2025:
            print("❌ 年份应在2000-2025之间")
            return

        month = int(input("请输入月份（1-12）: "))
        if month < 1 or month > 12:
            print("❌ 月份应在1-12之间")
            return

        output = input("请输入输出文件名（如添加城市后的天气.xlsx）: ").strip()
        if not output:
            output = f"添加{len(new_cities)}个城市后的天气数据.xlsx"
            print(f"使用默认文件名: {output}")

        if not output.endswith('.xlsx'):
            output += '.xlsx'

        print(f"\n开始添加 {len(new_cities)} 个城市的数据...")
        updater = WeatherExcelUpdater()
        updater.add_cities_to_excel(excel_file, new_cities, year, month, output)

    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 操作失败: {e}")

def option_5():
    """查看支持的城市列表"""
    print("\n🗺️ 支持的城市列表")
    cities = get_supported_cities()
    
    print("\n中文名 -> 拼音名")
    print("-" * 30)
    for chinese, pinyin in cities.items():
        print(f"{chinese:6} -> {pinyin}")
    
    print(f"\n总共支持 {len(cities)} 个城市")

def option_6():
    """批量爬取多个月份数据"""
    print("\n📅 批量爬取多个月份数据")
    print("此功能可以一次性爬取某个城市多个月份的数据")

    try:
        city = input("请输入城市名称（拼音，如jiaxing）: ").strip()
        if not city:
            print("❌ 城市名称不能为空")
            return

        year = int(input("请输入年份（如2024）: "))
        if year < 2000 or year > 2025:
            print("❌ 年份应在2000-2025之间")
            return

        start_month = int(input("请输入开始月份（1-12）: "))
        if start_month < 1 or start_month > 12:
            print("❌ 开始月份应在1-12之间")
            return

        end_month = int(input("请输入结束月份（1-12）: "))
        if end_month < 1 or end_month > 12:
            print("❌ 结束月份应在1-12之间")
            return

        if start_month > end_month:
            print("❌ 开始月份不能大于结束月份")
            return

        month_count = end_month - start_month + 1
        print(f"将爬取 {city} {year}年 {start_month}月到{end_month}月，共{month_count}个月的数据")

        output = input("请输入输出文件名（如年度天气数据.xlsx）: ").strip()
        if not output:
            output = f"{city}_{year}年{start_month}-{end_month}月天气数据.xlsx"
            print(f"使用默认文件名: {output}")

        if not output.endswith('.xlsx'):
            output += '.xlsx'

        confirm = input(f"确认爬取 {month_count} 个月的数据吗？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("操作已取消")
            return

        print(f"\n开始批量爬取数据...")
        scraper = WeatherScraper()
        all_data = []

        for month in range(start_month, end_month + 1):
            print(f"\n正在爬取 {year}年{month}月数据...")
            month_data = scraper.get_weather_data(city, year, month)
            if month_data:
                all_data.extend(month_data)
                print(f"✅ 获取到 {len(month_data)} 条数据")
            else:
                print(f"⚠️ {month}月数据获取失败")

        if all_data:
            scraper.save_to_excel(all_data, output)
            print(f"\n🎉 批量爬取完成！总共获取 {len(all_data)} 条数据")
        else:
            print("❌ 未获取到任何数据")

    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 操作失败: {e}")

def option_7():
    """预览和验证Excel文件"""
    print("\n📊 预览和验证Excel文件")
    print("此功能可以查看Excel文件的基本信息和数据质量")

    try:
        excel_file = input("请输入Excel文件路径: ").strip()
        if not excel_file:
            print("❌ 请输入文件路径")
            return

        if not os.path.exists(excel_file):
            print(f"❌ 文件 {excel_file} 不存在！")
            return

        import pandas as pd
        df = pd.read_excel(excel_file)

        print(f"\n📋 文件基本信息:")
        print(f"   文件名: {excel_file}")
        print(f"   数据行数: {len(df)}")
        print(f"   数据列数: {len(df.columns)}")
        print(f"   文件大小: {os.path.getsize(excel_file)} 字节")

        print(f"\n📊 列信息:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. {col}")

        print(f"\n🏙️ 城市统计:")
        if '城市' in df.columns:
            city_counts = df['城市'].value_counts()
            for city, count in city_counts.items():
                print(f"   {city}: {count} 条数据")

        print(f"\n📅 日期范围:")
        if '日期' in df.columns:
            dates = pd.to_datetime(df['日期'])
            print(f"   最早日期: {dates.min().strftime('%Y-%m-%d')}")
            print(f"   最晚日期: {dates.max().strftime('%Y-%m-%d')}")
            print(f"   日期跨度: {(dates.max() - dates.min()).days + 1} 天")

        print(f"\n🌡️ 温度统计:")
        if '最高温度(°C)' in df.columns and '最低温度(°C)' in df.columns:
            high_temps = df['最高温度(°C)'].dropna()
            low_temps = df['最低温度(°C)'].dropna()
            print(f"   最高温度: {high_temps.max()}°C")
            print(f"   最低温度: {low_temps.min()}°C")
            print(f"   平均最高温度: {high_temps.mean():.1f}°C")
            print(f"   平均最低温度: {low_temps.mean():.1f}°C")

        print(f"\n⚠️ 数据质量检查:")
        missing_data = df.isnull().sum()
        total_missing = missing_data.sum()
        if total_missing == 0:
            print("   ✅ 无缺失数据")
        else:
            print(f"   ❌ 发现 {total_missing} 处缺失数据:")
            for col, count in missing_data.items():
                if count > 0:
                    print(f"      {col}: {count} 处缺失")

        # 温度逻辑检查
        if '最高温度(°C)' in df.columns and '最低温度(°C)' in df.columns:
            temp_logic_errors = (df['最高温度(°C)'] < df['最低温度(°C)']).sum()
            if temp_logic_errors == 0:
                print("   ✅ 温度数据逻辑正确")
            else:
                print(f"   ❌ 发现 {temp_logic_errors} 处温度逻辑错误（最高温度 < 最低温度）")

        print(f"\n📖 数据预览（前5行）:")
        print(df.head().to_string(index=False))

    except Exception as e:
        print(f"❌ 操作失败: {e}")

def option_8():
    """测试网络连接"""
    print("\n🌐 测试网络连接")
    print("测试是否能正常访问天气网站...")

    try:
        import requests
        from datetime import datetime

        test_url = "https://www.tianqi24.com/jiaxing/history202403.html"
        print(f"正在测试访问: {test_url}")

        start_time = datetime.now()
        response = requests.get(test_url, timeout=10)
        end_time = datetime.now()

        response_time = (end_time - start_time).total_seconds()

        if response.status_code == 200:
            print(f"✅ 网络连接正常")
            print(f"   响应时间: {response_time:.2f} 秒")
            print(f"   状态码: {response.status_code}")
            print(f"   页面大小: {len(response.content)} 字节")

            # 简单检查页面内容
            if "嘉兴" in response.text and "天气" in response.text:
                print("   ✅ 页面内容正常")
            else:
                print("   ⚠️ 页面内容可能异常")
        else:
            print(f"❌ 网络连接异常")
            print(f"   状态码: {response.status_code}")

    except requests.exceptions.Timeout:
        print("❌ 连接超时，请检查网络连接")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请检查网络连接")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择功能（0-8）: ").strip()

            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                option_1()
            elif choice == '2':
                option_2()
            elif choice == '3':
                option_3()
            elif choice == '4':
                option_4()
            elif choice == '5':
                option_5()
            elif choice == '6':
                option_6()
            elif choice == '7':
                option_7()
            elif choice == '8':
                option_8()
            else:
                print("❌ 无效选择，请输入0-8之间的数字")

        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作，再见！")
            break
        except EOFError:
            print("\n\n👋 程序结束，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            print("请检查输入是否正确")

if __name__ == "__main__":
    main()
