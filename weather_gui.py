#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据爬取工具 - 图形界面版本
为没有编程经验的用户提供简单易用的界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from datetime import datetime
import pandas as pd

# 导入核心功能模块
try:
    from weather_scraper import WeatherScraper
    from update_weather_excel import WeatherExcelUpdater, CITY_MAPPING
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保 weather_scraper.py 和 update_weather_excel.py 文件存在")
    sys.exit(1)

class WeatherGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🌤️ 天气数据爬取工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 初始化组件
        self.scraper = WeatherScraper()
        self.updater = WeatherExcelUpdater()
        
        # 创建界面
        self.create_widgets()
        
        # 居中显示窗口
        self.center_window()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(pady=10)
        
        title_label = ttk.Label(title_frame, text="🌤️ 天气数据爬取工具", 
                               font=('Arial', 16, 'bold'))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="简单易用的天气数据获取工具", 
                                  font=('Arial', 10))
        subtitle_label.pack()
        
        # 创建笔记本（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 标签页1: 爬取新数据
        self.create_scrape_tab(notebook)
        
        # 标签页2: 更新现有文件
        self.create_update_tab(notebook)
        
        # 标签页3: 帮助信息
        self.create_help_tab(notebook)
        
        # 底部状态栏
        self.create_status_bar()
    
    def create_scrape_tab(self, notebook):
        """创建爬取数据标签页"""
        scrape_frame = ttk.Frame(notebook)
        notebook.add(scrape_frame, text="📥 爬取新数据")
        
        # 城市选择区域
        city_frame = ttk.LabelFrame(scrape_frame, text="选择城市", padding=10)
        city_frame.pack(fill='x', padx=10, pady=5)
        
        # 单个城市选择
        single_city_frame = ttk.Frame(city_frame)
        single_city_frame.pack(fill='x', pady=2)
        
        ttk.Label(single_city_frame, text="单个城市:").pack(side='left')
        self.single_city_var = tk.StringVar()
        city_combo = ttk.Combobox(single_city_frame, textvariable=self.single_city_var,
                                 values=list(CITY_MAPPING.keys()), width=15)
        city_combo.pack(side='left', padx=5)
        city_combo.set("嘉兴")  # 默认选择
        
        # 多个城市选择
        multi_city_frame = ttk.Frame(city_frame)
        multi_city_frame.pack(fill='x', pady=5)
        
        ttk.Label(multi_city_frame, text="或选择多个城市:").pack(anchor='w')
        
        # 创建城市复选框
        self.city_vars = {}
        cities_per_row = 4
        cities = list(CITY_MAPPING.keys())
        
        for i, city in enumerate(cities):
            if i % cities_per_row == 0:
                row_frame = ttk.Frame(multi_city_frame)
                row_frame.pack(fill='x', pady=2)
            
            var = tk.BooleanVar()
            self.city_vars[city] = var
            ttk.Checkbutton(row_frame, text=city, variable=var).pack(side='left', padx=10)
        
        # 时间选择区域
        time_frame = ttk.LabelFrame(scrape_frame, text="选择时间", padding=10)
        time_frame.pack(fill='x', padx=10, pady=5)
        
        time_input_frame = ttk.Frame(time_frame)
        time_input_frame.pack()
        
        ttk.Label(time_input_frame, text="年份:").pack(side='left')
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        year_spin = ttk.Spinbox(time_input_frame, from_=2020, to=2025, 
                               textvariable=self.year_var, width=8)
        year_spin.pack(side='left', padx=5)
        
        ttk.Label(time_input_frame, text="月份:").pack(side='left', padx=(20,0))
        self.month_var = tk.StringVar(value=str(datetime.now().month))
        month_spin = ttk.Spinbox(time_input_frame, from_=1, to=12, 
                                textvariable=self.month_var, width=8)
        month_spin.pack(side='left', padx=5)
        
        # 输出文件选择
        output_frame = ttk.LabelFrame(scrape_frame, text="输出文件", padding=10)
        output_frame.pack(fill='x', padx=10, pady=5)
        
        output_input_frame = ttk.Frame(output_frame)
        output_input_frame.pack(fill='x')
        
        self.output_file_var = tk.StringVar(value="天气数据.xlsx")
        ttk.Entry(output_input_frame, textvariable=self.output_file_var).pack(side='left', fill='x', expand=True)
        ttk.Button(output_input_frame, text="选择位置", 
                  command=self.choose_output_file).pack(side='right', padx=(5,0))
        
        # 开始爬取按钮
        button_frame = ttk.Frame(scrape_frame)
        button_frame.pack(pady=10)
        
        self.scrape_button = ttk.Button(button_frame, text="🚀 开始爬取", 
                                       command=self.start_scraping, style='Accent.TButton')
        self.scrape_button.pack()
    
    def create_update_tab(self, notebook):
        """创建更新文件标签页"""
        update_frame = ttk.Frame(notebook)
        notebook.add(update_frame, text="🔄 更新现有文件")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(update_frame, text="选择Excel文件", padding=10)
        file_frame.pack(fill='x', padx=10, pady=5)
        
        file_input_frame = ttk.Frame(file_frame)
        file_input_frame.pack(fill='x')
        
        self.excel_file_var = tk.StringVar()
        ttk.Entry(file_input_frame, textvariable=self.excel_file_var).pack(side='left', fill='x', expand=True)
        ttk.Button(file_input_frame, text="选择文件", 
                  command=self.choose_excel_file).pack(side='right', padx=(5,0))
        
        # 显示文件信息
        self.file_info_text = scrolledtext.ScrolledText(file_frame, height=4, width=60)
        self.file_info_text.pack(fill='x', pady=5)
        
        # 时间选择（更新用）
        update_time_frame = ttk.LabelFrame(update_frame, text="更新到指定时间", padding=10)
        update_time_frame.pack(fill='x', padx=10, pady=5)
        
        update_time_input_frame = ttk.Frame(update_time_frame)
        update_time_input_frame.pack()
        
        ttk.Label(update_time_input_frame, text="年份:").pack(side='left')
        self.update_year_var = tk.StringVar(value=str(datetime.now().year))
        ttk.Spinbox(update_time_input_frame, from_=2020, to=2025, 
                   textvariable=self.update_year_var, width=8).pack(side='left', padx=5)
        
        ttk.Label(update_time_input_frame, text="月份:").pack(side='left', padx=(20,0))
        self.update_month_var = tk.StringVar(value=str(datetime.now().month))
        ttk.Spinbox(update_time_input_frame, from_=1, to=12, 
                   textvariable=self.update_month_var, width=8).pack(side='left', padx=5)
        
        # 更新按钮
        update_button_frame = ttk.Frame(update_frame)
        update_button_frame.pack(pady=10)
        
        self.update_button = ttk.Button(update_button_frame, text="🔄 更新数据", 
                                       command=self.start_updating, style='Accent.TButton')
        self.update_button.pack()
    
    def create_help_tab(self, notebook):
        """创建帮助标签页"""
        help_frame = ttk.Frame(notebook)
        notebook.add(help_frame, text="❓ 帮助")
        
        help_text = scrolledtext.ScrolledText(help_frame, wrap=tk.WORD, padx=10, pady=10)
        help_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        help_content = """
🌤️ 天气数据爬取工具使用说明

📥 爬取新数据：
1. 选择要爬取的城市（可以选择单个城市或多个城市）
2. 设置年份和月份
3. 选择输出文件位置和名称
4. 点击"开始爬取"按钮

🔄 更新现有文件：
1. 选择已有的Excel文件
2. 设置要更新到的年份和月份
3. 点击"更新数据"按钮

📋 支持的城市：
海宁、杭州、金华、宁波、衢州、台州、温州、诸暨、
嘉兴、绍兴、湖州、丽水、舟山

📊 输出格式：
生成的Excel文件包含以下列：
- 城市：城市名称
- 日期：具体日期
- 日期类型：星期几
- 白天天气：白天天气状况
- 晚上天气：晚上天气状况
- 最高温度(°C)：当日最高温度
- 最低温度(°C)：当日最低温度

⚠️ 注意事项：
- 请确保网络连接正常
- 爬取过程中请耐心等待，不要关闭程序
- 建议不要频繁爬取，避免对服务器造成压力
- 如果遇到问题，请查看底部的状态信息

🔧 技术支持：
如果遇到问题，请检查：
1. 网络连接是否正常
2. 输入的年份月份是否合理
3. 文件路径是否正确
4. 是否有足够的磁盘空间
        """
        
        help_text.insert('1.0', help_content)
        help_text.config(state='disabled')
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill='x', side='bottom')
        
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var)
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.pack(side='right', padx=10, pady=5, fill='x', expand=True)

    def choose_output_file(self):
        """选择输出文件位置"""
        filename = filedialog.asksaveasfilename(
            title="选择保存位置",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)

    def choose_excel_file(self):
        """选择要更新的Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.excel_file_var.set(filename)
            self.show_file_info(filename)

    def show_file_info(self, filename):
        """显示Excel文件信息"""
        try:
            df = pd.read_excel(filename)
            cities = df['城市'].unique()
            info = f"文件: {os.path.basename(filename)}\n"
            info += f"数据量: {len(df)} 条\n"
            info += f"包含城市: {', '.join(cities)}\n"
            info += f"日期范围: {df['日期'].min()} 到 {df['日期'].max()}"

            self.file_info_text.delete('1.0', tk.END)
            self.file_info_text.insert('1.0', info)
        except Exception as e:
            self.file_info_text.delete('1.0', tk.END)
            self.file_info_text.insert('1.0', f"无法读取文件信息: {e}")

    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def start_scraping(self):
        """开始爬取数据"""
        # 验证输入
        if not self.validate_scrape_input():
            return

        # 禁用按钮，开始进度条
        self.scrape_button.config(state='disabled')
        self.progress.start()

        # 在新线程中执行爬取
        thread = threading.Thread(target=self.scrape_data_thread)
        thread.daemon = True
        thread.start()

    def validate_scrape_input(self):
        """验证爬取输入"""
        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())

            if year < 2020 or year > 2025:
                messagebox.showerror("错误", "年份应在2020-2025之间")
                return False

            if month < 1 or month > 12:
                messagebox.showerror("错误", "月份应在1-12之间")
                return False

            # 检查是否选择了城市
            selected_cities = self.get_selected_cities()
            if not selected_cities:
                messagebox.showerror("错误", "请至少选择一个城市")
                return False

            # 检查输出文件
            output_file = self.output_file_var.get().strip()
            if not output_file:
                messagebox.showerror("错误", "请指定输出文件名")
                return False

            return True

        except ValueError:
            messagebox.showerror("错误", "请输入有效的年份和月份")
            return False

    def get_selected_cities(self):
        """获取选择的城市列表"""
        selected_cities = []

        # 检查单个城市选择
        single_city = self.single_city_var.get().strip()
        if single_city and single_city in CITY_MAPPING:
            selected_cities.append(single_city)

        # 检查多个城市选择
        for city, var in self.city_vars.items():
            if var.get():
                selected_cities.append(city)

        # 去重
        return list(set(selected_cities))

    def scrape_data_thread(self):
        """爬取数据的线程函数"""
        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())
            output_file = self.output_file_var.get().strip()
            selected_cities = self.get_selected_cities()

            self.update_status(f"开始爬取 {len(selected_cities)} 个城市的数据...")

            all_data = []

            for i, chinese_city in enumerate(selected_cities, 1):
                if chinese_city in CITY_MAPPING:
                    pinyin_city = CITY_MAPPING[chinese_city]
                    self.update_status(f"正在爬取 {chinese_city} ({i}/{len(selected_cities)})...")

                    city_data = self.scraper.get_weather_data(pinyin_city, year, month)

                    # 将拼音城市名转换为中文
                    for record in city_data:
                        record['城市'] = chinese_city

                    all_data.extend(city_data)

            if all_data:
                self.update_status("正在保存数据...")
                self.scraper.save_to_excel(all_data, output_file)

                # 在主线程中显示成功消息
                self.root.after(0, lambda: self.scrape_completed(output_file, len(all_data)))
            else:
                self.root.after(0, lambda: self.scrape_failed("未获取到任何数据"))

        except Exception as e:
            self.root.after(0, lambda: self.scrape_failed(str(e)))

    def scrape_completed(self, output_file, data_count):
        """爬取完成"""
        self.progress.stop()
        self.scrape_button.config(state='normal')
        self.update_status("爬取完成")

        messagebox.showinfo("成功", f"成功爬取 {data_count} 条数据\n保存到: {output_file}")

    def scrape_failed(self, error_msg):
        """爬取失败"""
        self.progress.stop()
        self.scrape_button.config(state='normal')
        self.update_status("爬取失败")

        messagebox.showerror("错误", f"爬取失败: {error_msg}")

    def start_updating(self):
        """开始更新数据"""
        if not self.validate_update_input():
            return

        self.update_button.config(state='disabled')
        self.progress.start()

        thread = threading.Thread(target=self.update_data_thread)
        thread.daemon = True
        thread.start()

    def validate_update_input(self):
        """验证更新输入"""
        try:
            excel_file = self.excel_file_var.get().strip()
            if not excel_file:
                messagebox.showerror("错误", "请选择Excel文件")
                return False

            if not os.path.exists(excel_file):
                messagebox.showerror("错误", "选择的文件不存在")
                return False

            year = int(self.update_year_var.get())
            month = int(self.update_month_var.get())

            if year < 2020 or year > 2025:
                messagebox.showerror("错误", "年份应在2020-2025之间")
                return False

            if month < 1 or month > 12:
                messagebox.showerror("错误", "月份应在1-12之间")
                return False

            return True

        except ValueError:
            messagebox.showerror("错误", "请输入有效的年份和月份")
            return False

    def update_data_thread(self):
        """更新数据的线程函数"""
        try:
            excel_file = self.excel_file_var.get().strip()
            year = int(self.update_year_var.get())
            month = int(self.update_month_var.get())

            self.update_status("正在更新数据...")

            # 创建备份文件名
            backup_file = excel_file.replace('.xlsx', f'_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')

            self.updater.update_excel_data(excel_file, year, month, backup_file)

            self.root.after(0, lambda: self.update_completed(backup_file))

        except Exception as e:
            self.root.after(0, lambda: self.update_failed(str(e)))

    def update_completed(self, backup_file):
        """更新完成"""
        self.progress.stop()
        self.update_button.config(state='normal')
        self.update_status("更新完成")

        messagebox.showinfo("成功", f"数据更新完成\n备份文件: {backup_file}")

        # 刷新文件信息
        if self.excel_file_var.get():
            self.show_file_info(self.excel_file_var.get())

    def update_failed(self, error_msg):
        """更新失败"""
        self.progress.stop()
        self.update_button.config(state='normal')
        self.update_status("更新失败")

        messagebox.showerror("错误", f"更新失败: {error_msg}")


def main():
    """主函数"""
    root = tk.Tk()
    app = WeatherGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
