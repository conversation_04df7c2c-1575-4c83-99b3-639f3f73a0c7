/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.container {
    width: 400px;
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 8px 8px 0 0;
}

.logo {
    font-size: 32px;
    margin-bottom: 8px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.subtitle {
    font-size: 12px;
    opacity: 0.9;
}

/* 内容区域 */
.content {
    padding: 20px;
}

.section {
    margin-bottom: 20px;
}

.section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
}

/* 城市选择 */
.city-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 12px;
}

.city-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
}

.city-item:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.city-item input[type="checkbox"] {
    margin-right: 8px;
    accent-color: #667eea;
}

.city-item span {
    font-size: 13px;
    color: #495057;
}

.city-controls {
    display: flex;
    gap: 8px;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-small:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

/* 时间选择 */
.time-inputs {
    display: flex;
    gap: 16px;
}

.input-group {
    flex: 1;
}

.input-group label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 6px;
    color: #495057;
}

.input-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    font-size: 13px;
    color: #495057;
}

.input-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 按钮样式 */
.btn-primary, .btn-success {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-icon {
    font-size: 16px;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

/* 结果显示 */
#resultContent {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #495057;
}

.result-success {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb;
}

.result-error {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb;
}

/* 底部 */
.footer {
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.help-links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.help-links a {
    font-size: 12px;
    color: #6c757d;
    text-decoration: none;
    transition: color 0.2s ease;
}

.help-links a:hover {
    color: #667eea;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    overflow-y: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    line-height: 1;
}

.close:hover {
    color: #495057;
}

.modal-body {
    padding: 20px;
}

.modal-body h4 {
    color: #2c3e50;
    margin-bottom: 8px;
    margin-top: 16px;
}

.modal-body h4:first-child {
    margin-top: 0;
}

.modal-body ol, .modal-body ul {
    margin-left: 20px;
    margin-bottom: 16px;
}

.modal-body li {
    margin-bottom: 4px;
    color: #495057;
}

.modal-body p {
    color: #495057;
    line-height: 1.5;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
