#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome插件图标修复脚本
如果需要添加图标，运行此脚本会创建简单的PNG图标文件
"""

import os
import json
from PIL import Image, ImageDraw, ImageFont

def create_simple_icon(size, output_path):
    """创建简单的天气图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (74, 144, 226, 255))  # 蓝色背景
    draw = ImageDraw.Draw(img)
    
    # 绘制太阳
    sun_size = size // 4
    sun_x = size // 3
    sun_y = size // 3
    draw.ellipse([sun_x - sun_size//2, sun_y - sun_size//2, 
                  sun_x + sun_size//2, sun_y + sun_size//2], 
                 fill=(255, 215, 0, 255))  # 金色太阳
    
    # 绘制云朵
    cloud_size = size // 6
    cloud_x = size * 2 // 3
    cloud_y = size * 2 // 3
    
    # 云朵由几个圆组成
    for i, (dx, dy, r) in enumerate([(-cloud_size//2, 0, cloud_size//2), 
                                     (0, -cloud_size//3, cloud_size//3),
                                     (cloud_size//2, 0, cloud_size//2)]):
        draw.ellipse([cloud_x + dx - r, cloud_y + dy - r,
                      cloud_x + dx + r, cloud_y + dy + r],
                     fill=(255, 255, 255, 200))  # 半透明白色
    
    # 保存图像
    img.save(output_path, 'PNG')
    print(f"✓ 创建图标: {output_path}")

def update_manifest_with_icons():
    """更新manifest.json添加图标引用"""
    manifest_path = 'manifest.json'
    
    if not os.path.exists(manifest_path):
        print("❌ 未找到manifest.json文件")
        return
    
    with open(manifest_path, 'r', encoding='utf-8') as f:
        manifest = json.load(f)
    
    # 添加图标配置
    icons = {
        "16": "icons/icon16.png",
        "32": "icons/icon32.png", 
        "48": "icons/icon48.png",
        "128": "icons/icon128.png"
    }
    
    manifest["icons"] = icons
    manifest["action"]["default_icon"] = icons
    
    # 保存更新后的manifest
    with open(manifest_path, 'w', encoding='utf-8') as f:
        json.dump(manifest, f, indent=2, ensure_ascii=False)
    
    print("✓ 更新manifest.json添加图标引用")

def main():
    """主函数"""
    print("🔧 Chrome插件图标修复工具")
    print("=" * 30)
    
    # 检查是否安装了PIL
    try:
        from PIL import Image, ImageDraw
    except ImportError:
        print("❌ 需要安装Pillow库")
        print("运行: pip install Pillow")
        return
    
    # 创建icons目录
    icons_dir = 'icons'
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
        print(f"✓ 创建目录: {icons_dir}")
    
    # 创建不同尺寸的图标
    sizes = [16, 32, 48, 128]
    for size in sizes:
        icon_path = os.path.join(icons_dir, f'icon{size}.png')
        create_simple_icon(size, icon_path)
    
    # 询问是否更新manifest.json
    response = input("\n是否更新manifest.json添加图标引用？(y/n): ").lower()
    if response == 'y':
        update_manifest_with_icons()
    
    print("\n🎉 图标修复完成！")
    print("现在可以重新加载Chrome插件了。")

if __name__ == "__main__":
    main()
