# 天气网站数据爬取工具

这是一个用于从 [天气24网](https://www.tianqi24.com) 爬取历史天气数据的Python工具。

## 功能特点

- 🌤️ 支持爬取任意城市的历史天气数据
- 📅 支持指定年份和月份
- 🏙️ 支持批量爬取多个城市
- 📊 自动整理数据并导出为Excel格式
- 🔄 智能重试和错误处理
- ⏱️ 内置请求延迟，避免频繁访问

## 安装依赖

### 方法1: 使用虚拟环境（推荐）

```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 方法2: 直接安装

```bash
pip install requests beautifulsoup4 pandas openpyxl
```

## 使用方法

### 🚀 快速开始（推荐）

运行交互式使用指南：

```bash
source venv/bin/activate  # 激活虚拟环境
python 使用指南.py
```

这将启动一个交互式菜单，引导你完成各种操作。

### 📋 命令行使用

#### 1. 基本爬虫功能

```bash
# 爬取嘉兴2024年3月天气数据
python weather_scraper.py --city jiaxing --year 2024 --month 3 --output 嘉兴2024年03月天气.xlsx

# 爬取多个城市
python weather_scraper.py --cities jiaxing hangzhou ningbo --year 2024 --month 3 --output 多城市天气.xlsx
```

#### 2. 更新现有Excel文件

```bash
# 更新现有Excel文件中所有城市的数据
python update_weather_excel.py --excel 八月地方天气.xlsx --year 2024 --month 3 --output 更新后的数据.xlsx

# 向现有Excel文件添加新城市
python update_weather_excel.py --excel 八月地方天气.xlsx --year 2024 --month 3 --add-cities 嘉兴 绍兴 --output 添加城市后的数据.xlsx
```

#### 参数说明

**weather_scraper.py 参数：**
- `--city`: 城市名称（拼音），如 jiaxing、hangzhou、shanghai
- `--cities`: 多个城市名称（用空格分隔）
- `--year`: 年份，默认2024
- `--month`: 月份，默认3
- `--output`: 输出文件名，默认weather_data.xlsx

**update_weather_excel.py 参数：**
- `--excel`: 现有Excel文件路径（必需）
- `--year`: 年份（必需）
- `--month`: 月份（必需）
- `--output`: 输出文件路径（可选，默认覆盖原文件）
- `--add-cities`: 要添加的新城市列表（中文名，用空格分隔）

### Python脚本使用

```python
from weather_scraper import WeatherScraper

# 创建爬虫实例
scraper = WeatherScraper()

# 爬取单个城市数据
data = scraper.get_weather_data('jiaxing', 2024, 3)
scraper.save_to_excel(data, '嘉兴天气.xlsx')

# 爬取多个城市数据
cities = ['jiaxing', 'hangzhou', 'ningbo']
scraper.scrape_multiple_cities(cities, 2024, 3, '多城市天气.xlsx')
```

## 数据格式

输出的Excel文件包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 城市 | 城市名称 | jiaxing |
| 日期 | 日期 | 2024-03-01 |
| 日期类型 | 星期 | 星期五 |
| 白天天气 | 白天天气状况 | 晴 |
| 晚上天气 | 晚上天气状况 | 多云 |
| 最高温度(°C) | 当日最高温度 | 10 |
| 最低温度(°C) | 当日最低温度 | -2 |

## 支持的城市

工具支持天气24网站上的所有城市，城市名称需要使用拼音格式，例如：

- 嘉兴: jiaxing
- 杭州: hangzhou  
- 上海: shanghai
- 北京: beijing
- 广州: guangzhou
- 深圳: shenzhen

## 常见问题

### Q: 如何找到城市的拼音名称？
A: 可以访问 https://www.tianqi24.com 搜索你要的城市，URL中的城市名称就是拼音格式。

### Q: 爬取失败怎么办？
A: 
1. 检查网络连接
2. 确认城市名称拼音是否正确
3. 确认年份月份是否存在数据
4. 适当增加请求间隔时间

### Q: 可以爬取多长时间的数据？
A: 取决于网站的数据范围，一般可以爬取近几年的历史数据。

## 注意事项

1. 请合理使用，避免频繁请求对网站造成压力
2. 工具内置了随机延迟，请耐心等待
3. 如果遇到反爬虫限制，可以适当增加延迟时间
4. 数据仅供学习和研究使用

## 示例文件

运行 `example_usage.py` 查看更多使用示例：

```bash
python example_usage.py
```

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。
