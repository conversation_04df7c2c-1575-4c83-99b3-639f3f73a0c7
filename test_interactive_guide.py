#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交互式使用指南的脚本
"""

import os
import sys
import subprocess
import tempfile
import pandas as pd
from io import StringIO

def create_test_excel():
    """创建测试用的Excel文件"""
    test_data = [
        {'城市': '杭州', '日期': '2024-03-01', '日期类型': '星期五', '白天天气': '晴', '晚上天气': '多云', '最高温度(°C)': 15, '最低温度(°C)': 5},
        {'城市': '杭州', '日期': '2024-03-02', '日期类型': '星期六', '白天天气': '多云', '晚上天气': '阴', '最高温度(°C)': 12, '最低温度(°C)': 3},
        {'城市': '宁波', '日期': '2024-03-01', '日期类型': '星期五', '白天天气': '阴', '晚上天气': '雨', '最高温度(°C)': 13, '最低温度(°C)': 7},
    ]
    df = pd.DataFrame(test_data)
    test_file = 'test_interactive.xlsx'
    df.to_excel(test_file, index=False)
    return test_file

def test_option_5():
    """测试选项5：查看支持的城市列表"""
    print("🧪 测试选项5：查看支持的城市列表")
    
    try:
        # 模拟选项5的功能
        from update_weather_excel import CITY_MAPPING
        
        print("支持的城市列表:")
        for chinese, pinyin in list(CITY_MAPPING.items())[:5]:
            print(f"  {chinese} -> {pinyin}")
        
        print(f"✅ 城市映射功能正常，支持 {len(CITY_MAPPING)} 个城市")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_option_7():
    """测试选项7：预览和验证Excel文件"""
    print("\n🧪 测试选项7：预览和验证Excel文件")
    
    try:
        # 创建测试文件
        test_file = create_test_excel()
        print(f"创建测试文件: {test_file}")
        
        # 模拟选项7的功能
        df = pd.read_excel(test_file)
        
        print(f"文件基本信息:")
        print(f"  数据行数: {len(df)}")
        print(f"  数据列数: {len(df.columns)}")
        print(f"  文件大小: {os.path.getsize(test_file)} 字节")
        
        print(f"城市统计:")
        city_counts = df['城市'].value_counts()
        for city, count in city_counts.items():
            print(f"  {city}: {count} 条数据")
        
        # 清理测试文件
        os.remove(test_file)
        print("✅ Excel预览功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_option_8():
    """测试选项8：测试网络连接"""
    print("\n🧪 测试选项8：测试网络连接")
    
    try:
        import requests
        from datetime import datetime
        
        test_url = "https://www.tianqi24.com/jiaxing/history202403.html"
        print(f"测试访问: {test_url}")
        
        start_time = datetime.now()
        response = requests.get(test_url, timeout=10)
        end_time = datetime.now()
        
        response_time = (end_time - start_time).total_seconds()
        
        if response.status_code == 200:
            print(f"✅ 网络连接正常")
            print(f"  响应时间: {response_time:.2f} 秒")
            print(f"  状态码: {response.status_code}")
            return True
        else:
            print(f"⚠️ 网络连接异常，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("⚠️ 连接超时")
        return False
    except requests.exceptions.ConnectionError:
        print("⚠️ 连接失败")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_input_validation():
    """测试输入验证功能"""
    print("\n🧪 测试输入验证功能")
    
    try:
        # 测试年份验证
        def validate_year(year):
            return 2000 <= year <= 2025
        
        # 测试月份验证
        def validate_month(month):
            return 1 <= month <= 12
        
        # 测试文件扩展名
        def ensure_xlsx_extension(filename):
            if not filename.endswith('.xlsx'):
                return filename + '.xlsx'
            return filename
        
        # 运行测试
        assert validate_year(2024) == True
        assert validate_year(1999) == False
        assert validate_year(2026) == False
        
        assert validate_month(3) == True
        assert validate_month(0) == False
        assert validate_month(13) == False
        
        assert ensure_xlsx_extension('test') == 'test.xlsx'
        assert ensure_xlsx_extension('test.xlsx') == 'test.xlsx'
        
        print("✅ 输入验证功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理功能"""
    print("\n🧪 测试错误处理功能")
    
    try:
        # 测试文件不存在的情况
        non_existent_file = "non_existent_file.xlsx"
        file_exists = os.path.exists(non_existent_file)
        
        if not file_exists:
            print("✅ 文件不存在检查正常")
        else:
            print("⚠️ 测试文件意外存在")
        
        # 测试空输入处理
        def handle_empty_input(input_str):
            return input_str.strip() != ""
        
        assert handle_empty_input("") == False
        assert handle_empty_input("  ") == False
        assert handle_empty_input("test") == True
        
        print("✅ 错误处理功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_menu_display():
    """测试菜单显示功能"""
    print("\n🧪 测试菜单显示功能")
    
    try:
        # 捕获菜单输出
        from 使用指南 import show_menu
        
        # 重定向stdout来捕获输出
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        show_menu()
        
        # 恢复stdout
        sys.stdout = old_stdout
        
        menu_output = captured_output.getvalue()
        
        # 检查菜单内容
        expected_items = [
            "天气数据爬取工具",
            "1. 爬取单个城市天气数据",
            "2. 爬取多个城市天气数据",
            "3. 更新现有Excel文件",
            "4. 向Excel文件添加新城市",
            "5. 查看支持的城市列表",
            "6. 批量爬取多个月份数据",
            "7. 预览和验证Excel文件",
            "8. 测试网络连接",
            "0. 退出"
        ]
        
        all_present = all(item in menu_output for item in expected_items)
        
        if all_present:
            print("✅ 菜单显示功能正常")
            return True
        else:
            print("❌ 菜单内容不完整")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始交互式使用指南综合测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("菜单显示", test_menu_display()))
    test_results.append(("城市列表", test_option_5()))
    test_results.append(("Excel预览", test_option_7()))
    test_results.append(("网络连接", test_option_8()))
    test_results.append(("输入验证", test_input_validation()))
    test_results.append(("错误处理", test_error_handling()))
    
    # 统计结果
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！交互式使用指南功能完备")
    else:
        print(f"⚠️ 有 {total - passed} 项测试失败，需要检查")
    
    return passed == total

def main():
    """主函数"""
    try:
        success = run_comprehensive_test()
        
        if success:
            print("\n🎯 建议测试交互式使用指南:")
            print("运行命令: python 使用指南.py")
            print("然后依次测试各个选项的功能")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
