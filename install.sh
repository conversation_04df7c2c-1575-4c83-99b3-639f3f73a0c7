#!/bin/bash

echo ""
echo "========================================"
echo "🌤️ 天气数据爬取工具 - 安装脚本"
echo "========================================"
echo ""

# 检查Python环境
echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 错误: 未找到Python环境"
        echo ""
        echo "请先安装Python 3.7或更高版本："
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✓ Python环境检查通过"
echo ""

# 检查pip
echo "正在检查pip..."
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "❌ 错误: 未找到pip"
        echo "请确保pip已正确安装"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

echo "✓ pip检查通过"
echo ""

# 安装依赖
echo "正在安装依赖包..."
echo "这可能需要几分钟时间，请耐心等待..."
echo ""

$PIP_CMD install -r requirements.txt

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 依赖安装失败"
    echo ""
    echo "可能的解决方案："
    echo "1. 检查网络连接"
    echo "2. 尝试使用国内镜像源："
    echo "   $PIP_CMD install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/"
    echo "3. 手动安装各个包："
    echo "   $PIP_CMD install requests beautifulsoup4 pandas openpyxl flask"
    echo ""
    exit 1
fi

echo ""
echo "✅ 依赖安装完成！"
echo ""
echo "🚀 现在可以使用天气数据爬取工具了："
echo ""
echo "启动方式："
echo "1. 运行：$PYTHON_CMD 启动工具.py （推荐）"
echo ""
echo "其他使用方式："
echo "• 图形界面：$PYTHON_CMD weather_gui.py"
echo "• 网页版本：$PYTHON_CMD weather_web.py"
echo "• 命令行版：$PYTHON_CMD 使用指南.py"
echo ""
echo "📖 详细使用说明请查看 '用户使用指南.md'"
echo ""

# 设置执行权限
chmod +x 启动工具.py
chmod +x weather_gui.py
chmod +x weather_web.py
chmod +x 使用指南.py

echo "✓ 文件权限设置完成"
echo ""
echo "安装完成！按任意键继续..."
read -n 1
