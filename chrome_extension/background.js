// Background Script - 后台服务工作者
console.log('天气数据爬取工具 Background Script 已启动');

// 监听插件安装
chrome.runtime.onInstalled.addListener((details) => {
    console.log('插件已安装/更新:', details.reason);
    
    if (details.reason === 'install') {
        // 首次安装时的初始化
        console.log('欢迎使用天气数据爬取工具！');
        
        // 设置默认配置
        chrome.storage.local.set({
            weatherSettings: {
                year: new Date().getFullYear().toString(),
                month: (new Date().getMonth() + 1).toString(),
                cities: ['嘉兴']
            }
        });
    }
});

// 监听来自popup或content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Background收到消息:', request);
    
    if (request.action === 'downloadData') {
        // 处理数据下载
        handleDataDownload(request.data, request.filename)
            .then(result => sendResponse({ success: true, result }))
            .catch(error => sendResponse({ success: false, error: error.message }));
        
        return true; // 保持消息通道开放
    }
    
    if (request.action === 'getStorageData') {
        // 获取存储的数据
        chrome.storage.local.get([request.key], (result) => {
            sendResponse({ success: true, data: result[request.key] });
        });
        
        return true;
    }
    
    if (request.action === 'setStorageData') {
        // 设置存储数据
        const data = {};
        data[request.key] = request.value;
        
        chrome.storage.local.set(data, () => {
            sendResponse({ success: true });
        });
        
        return true;
    }
});

// 处理数据下载
async function handleDataDownload(data, filename) {
    try {
        // 创建CSV内容
        const headers = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)'];
        let csvContent = headers.join(',') + '\n';
        
        data.forEach(row => {
            const csvRow = [
                row.city || '',
                row.date || '',
                row.weekday || '',
                row.dayWeather || '',
                row.nightWeather || '',
                row.highTemp || '',
                row.lowTemp || ''
            ].map(field => `"${field}"`).join(','); // 用引号包围字段以处理特殊字符
            csvContent += csvRow + '\n';
        });
        
        // 添加BOM以支持中文
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // 创建下载URL
        const url = URL.createObjectURL(blob);
        
        // 使用Chrome下载API
        const downloadId = await chrome.downloads.download({
            url: url,
            filename: filename || `天气数据_${new Date().toISOString().slice(0, 10)}.csv`,
            saveAs: true
        });
        
        console.log('下载已开始，ID:', downloadId);
        return downloadId;
        
    } catch (error) {
        console.error('下载处理失败:', error);
        throw error;
    }
}

// 监听下载状态变化
chrome.downloads.onChanged.addListener((downloadDelta) => {
    if (downloadDelta.state && downloadDelta.state.current === 'complete') {
        console.log('下载完成:', downloadDelta.id);
        
        // 可以在这里添加下载完成的通知
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: '天气数据爬取工具',
            message: '数据下载完成！'
        });
    }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当用户访问天气网站时，可以在这里做一些初始化工作
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('tianqi24.com')) {
        console.log('用户访问了天气网站:', tab.url);
    }
});

// 定期清理临时数据
setInterval(() => {
    // 清理超过24小时的临时存储数据
    chrome.storage.local.get(null, (items) => {
        const now = Date.now();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;
        
        Object.keys(items).forEach(key => {
            if (key.startsWith('temp_') && items[key].timestamp < oneDayAgo) {
                chrome.storage.local.remove(key);
                console.log('清理过期临时数据:', key);
            }
        });
    });
}, 60 * 60 * 1000); // 每小时检查一次

// 错误处理
chrome.runtime.onSuspend.addListener(() => {
    console.log('Background script 即将被挂起');
});

// 处理未捕获的错误
self.addEventListener('error', (event) => {
    console.error('Background script 发生错误:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Background script 未处理的Promise拒绝:', event.reason);
});

console.log('Background Script 初始化完成');
