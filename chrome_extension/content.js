// Content Script - 在天气网站页面中运行
console.log('天气数据爬取工具 Content Script 已加载');

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'scrapeCity') {
        console.log('收到爬取请求:', request);
        
        scrapeWeatherData(request.city, request.year, request.month)
            .then(data => {
                sendResponse({ success: true, data: data });
            })
            .catch(error => {
                console.error('爬取失败:', error);
                sendResponse({ success: false, error: error.message });
            });
        
        return true; // 保持消息通道开放
    }
});

async function scrapeWeatherData(city, year, month) {
    try {
        // 构建URL
        const url = `https://www.tianqi24.com/lishi/${city}/${year}${month.toString().padStart(2, '0')}.html`;
        console.log('正在访问:', url);
        
        // 获取页面内容
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const html = await response.text();
        
        // 解析HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        // 提取天气数据
        const weatherData = parseWeatherData(doc, city, year, month);
        
        console.log(`成功获取 ${weatherData.length} 条数据`);
        return weatherData;
        
    } catch (error) {
        console.error('爬取失败:', error);
        throw error;
    }
}

function parseWeatherData(doc, city, year, month) {
    const weatherData = [];
    
    try {
        // 查找天气数据列表
        const weatherList = doc.querySelectorAll('li');
        
        for (const li of weatherList) {
            const text = li.textContent.trim();
            
            // 使用正则表达式匹配日期格式 (如: 03-01)
            const dateMatch = text.match(/(\d{2})-(\d{2})/);
            if (!dateMatch) continue;
            
            const monthDay = dateMatch[0];
            const fullDate = `${year}-${monthDay}`;
            
            // 解析天气信息
            const weatherInfo = parseWeatherInfo(text, fullDate, city);
            if (weatherInfo) {
                weatherData.push(weatherInfo);
            }
        }
        
        // 如果上面的方法没有找到数据，尝试其他解析方法
        if (weatherData.length === 0) {
            return parseAlternativeFormat(doc, city, year, month);
        }
        
        return weatherData;
        
    } catch (error) {
        console.error('解析数据失败:', error);
        return [];
    }
}

function parseWeatherInfo(text, date, city) {
    try {
        // 提取温度信息
        const tempPattern = /(\d+)℃.*?(-?\d+)℃/;
        const tempMatch = text.match(tempPattern);
        
        if (!tempMatch) return null;
        
        const highTemp = parseInt(tempMatch[1]);
        const lowTemp = parseInt(tempMatch[2]);
        
        // 提取天气信息
        // 先移除日期部分
        const weatherPart = text.replace(/^\d{2}-\d{2}/, '');
        
        // 查找天气关键词
        const weatherKeywords = ['晴', '多云', '阴', '雨', '小雨', '中雨', '大雨', '暴雨', '雪', '小雪', '中雪', '大雪', '雾', '霾', '沙尘'];
        const foundWeathers = [];
        
        for (const keyword of weatherKeywords) {
            if (weatherPart.includes(keyword)) {
                foundWeathers.push(keyword);
            }
        }
        
        // 默认天气
        const dayWeather = foundWeathers[0] || '未知';
        const nightWeather = foundWeathers[1] || foundWeathers[0] || '未知';
        
        // 获取星期信息
        const weekday = getWeekday(date);
        
        return {
            city: city,
            date: date,
            weekday: weekday,
            dayWeather: dayWeather,
            nightWeather: nightWeather,
            highTemp: highTemp,
            lowTemp: lowTemp
        };
        
    } catch (error) {
        console.error('解析天气信息失败:', error);
        return null;
    }
}

function parseAlternativeFormat(doc, city, year, month) {
    const weatherData = [];
    
    try {
        // 查找所有可能包含天气数据的元素
        const elements = doc.querySelectorAll('tr, div, li');
        
        for (const element of elements) {
            const text = element.textContent.trim();
            
            // 查找日期模式
            const datePattern = new RegExp(`${month.toString().padStart(2, '0')}-(\\d{2})`);
            const dateMatch = text.match(datePattern);
            
            if (dateMatch) {
                const day = dateMatch[1];
                const fullDate = `${year}-${month.toString().padStart(2, '0')}-${day}`;
                
                // 提取温度
                const tempPattern = /(\d+)℃.*?(-?\d+)℃/;
                const tempMatch = text.match(tempPattern);
                
                if (tempMatch) {
                    const highTemp = parseInt(tempMatch[1]);
                    const lowTemp = parseInt(tempMatch[2]);
                    
                    // 提取天气
                    const weatherPattern = /(晴|多云|阴|雨|小雨|中雨|大雨|暴雨|雪|雾|霾|沙)/g;
                    const weatherMatches = text.match(weatherPattern) || [];
                    
                    const dayWeather = weatherMatches[0] || '未知';
                    const nightWeather = weatherMatches[1] || dayWeather;
                    
                    const weekday = getWeekday(fullDate);
                    
                    weatherData.push({
                        city: city,
                        date: fullDate,
                        weekday: weekday,
                        dayWeather: dayWeather,
                        nightWeather: nightWeather,
                        highTemp: highTemp,
                        lowTemp: lowTemp
                    });
                }
            }
        }
        
    } catch (error) {
        console.error('备用解析失败:', error);
    }
    
    return weatherData;
}

function getWeekday(dateString) {
    try {
        const date = new Date(dateString);
        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        return weekdays[date.getDay()];
    } catch (error) {
        return '未知';
    }
}

// 添加一些工具函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 监听页面加载完成
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，Content Script 准备就绪');
    });
} else {
    console.log('页面已加载，Content Script 准备就绪');
}
