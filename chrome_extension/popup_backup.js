// 备用版本 - 使用fetch API直接获取数据

// 城市映射表
const CITY_MAPPING = {
    '海宁': 'haining',
    '杭州': 'hangzhou', 
    '金华': 'jinhua',
    '宁波': 'ningbo',
    '衢州': 'quzhou',
    '台州': 'taizhou',
    '温州': 'wenzhou',
    '诸暨': 'zhuji',
    '嘉兴': 'jiaxing',
    '绍兴': 'shaoxing',
    '湖州': 'huzhou',
    '丽水': 'lishui',
    '舟山': 'zhoushan'
};

// DOM元素
let startButton, progressSection, resultSection, progressFill, progressText, resultContent, downloadBtn;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    bindEvents();
    loadSettings();
});

function initializeElements() {
    startButton = document.getElementById('startScrape');
    progressSection = document.getElementById('progressSection');
    resultSection = document.getElementById('resultSection');
    progressFill = document.getElementById('progressFill');
    progressText = document.getElementById('progressText');
    resultContent = document.getElementById('resultContent');
    downloadBtn = document.getElementById('downloadBtn');
}

function bindEvents() {
    document.getElementById('selectAll').addEventListener('click', selectAllCities);
    document.getElementById('clearAll').addEventListener('click', clearAllCities);
    startButton.addEventListener('click', startScraping);
    document.getElementById('helpBtn').addEventListener('click', showHelp);
    document.getElementById('aboutBtn').addEventListener('click', showAbout);
    document.querySelector('.close').addEventListener('click', closeModal);
    document.getElementById('helpModal').addEventListener('click', function(e) {
        if (e.target === this) closeModal();
    });
    document.getElementById('year').addEventListener('change', saveSettings);
    document.getElementById('month').addEventListener('change', saveSettings);
}

function selectAllCities() {
    document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
    });
    saveSettings();
}

function clearAllCities() {
    document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    saveSettings();
}

function getSelectedCities() {
    const selected = [];
    document.querySelectorAll('.city-item input[type="checkbox"]:checked').forEach(checkbox => {
        selected.push(checkbox.value);
    });
    return selected;
}

async function startScraping() {
    const selectedCities = getSelectedCities();
    const year = parseInt(document.getElementById('year').value);
    const month = parseInt(document.getElementById('month').value);
    
    if (selectedCities.length === 0) {
        showError('请至少选择一个城市');
        return;
    }
    
    showProgress();
    startButton.disabled = true;
    startButton.textContent = '爬取中...';
    
    try {
        await scrapeWeatherDataDirect(selectedCities, year, month);
    } catch (error) {
        console.error('爬取失败:', error);
        showResult(false, `爬取失败: ${error.message}`);
    } finally {
        startButton.disabled = false;
        startButton.textContent = '🚀 开始爬取';
    }
}

async function scrapeWeatherDataDirect(cities, year, month) {
    const allData = [];
    const totalCities = cities.length;
    let successCount = 0;
    let errorMessages = [];
    
    for (let i = 0; i < cities.length; i++) {
        const chineseCity = cities[i];
        const pinyinCity = CITY_MAPPING[chineseCity];
        
        if (!pinyinCity) {
            errorMessages.push(`${chineseCity}: 城市映射未找到`);
            continue;
        }
        
        updateProgress(
            Math.round((i / totalCities) * 85), 
            `正在爬取 ${chineseCity} (${i + 1}/${totalCities})...`
        );
        
        try {
            const cityData = await fetchWeatherData(pinyinCity, year, month, chineseCity);
            
            if (cityData && cityData.length > 0) {
                allData.push(...cityData);
                successCount++;
                console.log(`✓ ${chineseCity}: 获取到 ${cityData.length} 条数据`);
            } else {
                errorMessages.push(`${chineseCity}: 未获取到数据`);
            }
            
            if (i < cities.length - 1) {
                await sleep(2000);
            }
            
        } catch (error) {
            console.error(`❌ 爬取 ${chineseCity} 失败:`, error);
            errorMessages.push(`${chineseCity}: ${error.message}`);
        }
    }
    
    updateProgress(95, '正在整理数据...');
    
    if (allData.length > 0) {
        const sortedData = allData.sort((a, b) => new Date(a.date) - new Date(b.date));
        let resultMessage = `成功爬取 ${sortedData.length} 条数据 (${successCount}/${totalCities} 个城市)`;
        
        if (errorMessages.length > 0) {
            resultMessage += `\n\n部分失败:\n${errorMessages.slice(0, 3).join('\n')}`;
        }
        
        updateProgress(100, '爬取完成！');
        showResult(true, resultMessage, sortedData);
    } else {
        const resultMessage = '未获取到任何数据\n\n错误详情:\n' + errorMessages.slice(0, 5).join('\n');
        showResult(false, resultMessage);
    }
}

async function fetchWeatherData(city, year, month, chineseName) {
    const monthStr = month.toString().padStart(2, '0');
    // 修正URL格式：使用完整的年份月份格式 historyYYYYMM.html
    const url = `https://www.tianqi24.com/${city}/history${year}${monthStr}.html`;

    console.log(`🌐 正在获取: ${url}`);
    
    try {
        // 使用Chrome的fetch API
        const response = await fetch(url, {
            method: 'GET',
            mode: 'cors',
            credentials: 'omit'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const html = await response.text();
        console.log(`📄 获取到HTML内容，长度: ${html.length}`);
        
        // 解析HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        return parseWeatherFromHTML(doc, year, month, chineseName);
        
    } catch (error) {
        console.error(`获取 ${chineseName} 数据失败:`, error);
        throw error;
    }
}

function parseWeatherFromHTML(doc, year, month, chineseName) {
    const weatherData = [];

    try {
        console.log(`🔍 开始解析 ${chineseName} 的HTML数据`);
        console.log('页面标题:', doc.title);

        // 根据真实网站结构，数据是以列表项形式展示的
        const listItems = doc.querySelectorAll('li');
        console.log(`找到 ${listItems.length} 个列表项`);

        for (let i = 0; i < listItems.length; i++) {
            const li = listItems[i];
            const text = li.textContent.trim();

            // 跳过太短的文本（可能是导航或其他内容）
            if (text.length < 10) continue;

            console.log(`处理列表项 ${i + 1}: ${text.substring(0, 100)}`);

            // 匹配日期格式 MM-DD
            const monthStr = month.toString().padStart(2, '0');
            const datePattern = new RegExp(`${monthStr}-(\\d{2})`);
            const dateMatch = text.match(datePattern);

            if (dateMatch) {
                console.log(`找到日期匹配: ${dateMatch[0]}`);

                const dayStr = dateMatch[1];
                const fullDate = `${year}-${monthStr}-${dayStr}`;

                // 提取温度信息 (格式: 38℃ 29℃)
                const tempPattern = /(\d+)℃[^\d]*(\d+)℃/;
                const tempMatch = text.match(tempPattern);

                if (tempMatch) {
                    const highTemp = parseInt(tempMatch[1]);
                    const lowTemp = parseInt(tempMatch[2]);

                    console.log(`提取温度: 高温${highTemp}℃, 低温${lowTemp}℃`);

                    // 验证温度合理性
                    if (highTemp >= -50 && highTemp <= 60 &&
                        lowTemp >= -50 && lowTemp <= 60 &&
                        lowTemp <= highTemp) {

                        // 提取天气信息
                        let dayWeather = '未知';
                        let nightWeather = '未知';

                        // 查找天气关键词，优先处理带斜杠的格式
                        if (text.includes(' / ')) {
                            // 格式: "阴 / 多云"
                            const weatherPart = text.split('℃')[0]; // 取温度前的部分
                            const weatherMatch = weatherPart.match(/([^\d\s]+)\s*\/\s*([^\d\s]+)/);
                            if (weatherMatch) {
                                dayWeather = weatherMatch[1].trim();
                                nightWeather = weatherMatch[2].trim();
                            }
                        } else {
                            // 单一天气状况，查找天气关键词
                            const weatherKeywords = ['晴', '多云', '阴', '雨', '小雨', '中雨', '大雨', '暴雨', '雷雨', '雪', '雾', '霾'];
                            for (const keyword of weatherKeywords) {
                                if (text.includes(keyword)) {
                                    dayWeather = nightWeather = keyword;
                                    break;
                                }
                            }
                        }

                        console.log(`提取天气: 白天=${dayWeather}, 晚上=${nightWeather}`);

                        // 获取星期信息
                        const dateObj = new Date(fullDate);
                        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                        const weekday = weekdays[dateObj.getDay()];

                        weatherData.push({
                            city: chineseName,
                            date: fullDate,
                            weekday: weekday,
                            dayWeather: dayWeather,
                            nightWeather: nightWeather,
                            highTemp: highTemp,
                            lowTemp: lowTemp
                        });

                        console.log(`✓ 解析成功: ${fullDate} ${dayWeather}/${nightWeather} ${highTemp}℃/${lowTemp}℃`);
                    } else {
                        console.warn(`温度数据异常: 高温${highTemp}℃, 低温${lowTemp}℃`);
                    }
                } else {
                    console.log(`未找到温度信息: ${text.substring(0, 50)}`);
                }
            }
        }

        // 如果列表项解析失败，尝试查找表格
        if (weatherData.length === 0) {
            console.log('列表项解析失败，尝试查找表格...');
            const tables = doc.querySelectorAll('table');
            console.log(`找到 ${tables.length} 个表格`);

            if (tables.length > 0) {
                // 处理表格数据（备用方法）
                for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
                    const table = tables[tableIndex];
                    const rows = table.querySelectorAll('tr');
                    console.log(`表格 ${tableIndex + 1} 有 ${rows.length} 行`);

                    for (let i = 1; i < rows.length; i++) {
                        const row = rows[i];
                        const cells = row.querySelectorAll('td');

                        if (cells.length >= 4) {
                            const rowText = row.textContent.trim();
                            const extracted = extractFromText(rowText, year, month, chineseName);
                            if (extracted) {
                                weatherData.push(extracted);
                                console.log(`✓ 从表格提取: ${extracted.date} ${extracted.dayWeather}/${extracted.nightWeather} ${extracted.highTemp}℃/${extracted.lowTemp}℃`);
                            }
                        }
                    }
                }
            }
        }

        console.log(`🎉 ${chineseName} 解析完成，共 ${weatherData.length} 条数据`);

        // 如果没有数据，输出页面内容用于调试
        if (weatherData.length === 0) {
            console.log('❌ 未提取到任何数据，输出页面内容用于调试:');
            const bodyText = doc.body.textContent;
            const lines = bodyText.split('\n').filter(line => line.trim().length > 0);
            console.log('页面主要文本内容（前20行）:');
            lines.slice(0, 20).forEach((line, index) => {
                console.log(`${index + 1}: ${line.trim().substring(0, 100)}`);
            });
        }

        return weatherData;

    } catch (error) {
        console.error(`解析 ${chineseName} HTML失败:`, error);
        return [];
    }
}

// 从文本中提取天气信息的辅助函数 - 针对真实数据格式优化
function extractFromText(text, year, month, chineseName) {
    try {
        // 匹配日期格式: MM-DD
        const monthStr = month.toString().padStart(2, '0');
        const datePattern = new RegExp(`${monthStr}-(\\d{2})`);
        const dateMatch = text.match(datePattern);

        if (!dateMatch) return null;

        const dayStr = dateMatch[1];
        const fullDate = `${year}-${monthStr}-${dayStr}`;

        // 提取温度信息 - 适配真实格式 "38℃ 29℃"
        const tempPattern = /(\d+)℃[^\d]*(\d+)℃/;
        const tempMatch = text.match(tempPattern);

        if (!tempMatch) return null;

        const highTemp = parseInt(tempMatch[1]);
        const lowTemp = parseInt(tempMatch[2]);

        // 验证温度合理性
        if (highTemp < -50 || highTemp > 60 || lowTemp < -50 || lowTemp > 60 || lowTemp > highTemp) {
            return null;
        }

        // 提取天气信息 - 处理真实格式
        let dayWeather = '未知';
        let nightWeather = '未知';

        // 优先处理带斜杠的格式 "阴 / 多云"
        if (text.includes(' / ')) {
            const weatherPart = text.split('℃')[0]; // 取第一个温度前的部分
            const weatherMatch = weatherPart.match(/([^\d\s]+)\s*\/\s*([^\d\s]+)/);
            if (weatherMatch) {
                dayWeather = weatherMatch[1].replace(/\*\*/g, '').trim(); // 移除加粗标记
                nightWeather = weatherMatch[2].replace(/\*\*/g, '').trim();
            }
        } else {
            // 单一天气状况，查找天气关键词
            const weatherKeywords = ['晴', '多云', '阴', '雨', '小雨', '中雨', '大雨', '暴雨', '雷雨', '雪', '雾', '霾'];
            for (const keyword of weatherKeywords) {
                if (text.includes(keyword)) {
                    dayWeather = nightWeather = keyword;
                    break;
                }
            }
        }

        // 获取星期信息
        const dateObj = new Date(fullDate);
        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        const weekday = weekdays[dateObj.getDay()];

        return {
            city: chineseName,
            date: fullDate,
            weekday: weekday,
            dayWeather: dayWeather,
            nightWeather: nightWeather,
            highTemp: highTemp,
            lowTemp: lowTemp
        };

    } catch (error) {
        console.error('文本解析错误:', error);
        return null;
    }
}

function showProgress() {
    progressSection.style.display = 'block';
    resultSection.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '准备中...';
}

function updateProgress(percent, message) {
    progressFill.style.width = percent + '%';
    progressText.textContent = message;
}

function showResult(success, message, data = null) {
    progressSection.style.display = 'none';
    resultSection.style.display = 'block';
    
    resultContent.className = success ? 'result-success' : 'result-error';
    resultContent.innerHTML = '';
    
    const messageDiv = document.createElement('div');
    messageDiv.style.whiteSpace = 'pre-line';
    messageDiv.textContent = message;
    resultContent.appendChild(messageDiv);
    
    if (success && data && data.length > 0) {
        downloadBtn.style.display = 'block';
        downloadBtn.onclick = function() {
            downloadExcel(data);
        };
        
        const statsDiv = document.createElement('div');
        statsDiv.style.marginTop = '10px';
        statsDiv.style.fontSize = '12px';
        statsDiv.style.color = '#666';
        
        const cityStats = {};
        data.forEach(item => {
            cityStats[item.city] = (cityStats[item.city] || 0) + 1;
        });
        
        const statsText = Object.entries(cityStats)
            .map(([city, count]) => `${city}: ${count}条`)
            .join(', ');
        
        statsDiv.textContent = `数据分布: ${statsText}`;
        resultContent.appendChild(statsDiv);
    } else {
        downloadBtn.style.display = 'none';
    }
}

function showError(message) {
    alert('错误: ' + message);
}

function downloadExcel(data) {
    try {
        const headers = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)'];
        let csvContent = headers.join(',') + '\n';
        
        data.forEach(row => {
            const csvRow = [
                row.city || '',
                row.date || '',
                row.weekday || '',
                row.dayWeather || '',
                row.nightWeather || '',
                row.highTemp || '',
                row.lowTemp || ''
            ].join(',');
            csvContent += csvRow + '\n';
        });
        
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        
        const now = new Date();
        const filename = `天气数据_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}_${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}.csv`;
        
        chrome.downloads.download({
            url: URL.createObjectURL(blob),
            filename: filename,
            saveAs: true
        });
        
    } catch (error) {
        console.error('下载失败:', error);
        showError('下载失败: ' + error.message);
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function saveSettings() {
    const settings = {
        year: document.getElementById('year').value,
        month: document.getElementById('month').value,
        cities: getSelectedCities()
    };
    chrome.storage.local.set({ weatherSettings: settings });
}

function loadSettings() {
    chrome.storage.local.get(['weatherSettings'], function(result) {
        if (result.weatherSettings) {
            const settings = result.weatherSettings;
            if (settings.year) document.getElementById('year').value = settings.year;
            if (settings.month) document.getElementById('month').value = settings.month;
            if (settings.cities) {
                document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = settings.cities.includes(checkbox.value);
                });
            }
        }
    });
}

function showHelp() {
    document.getElementById('helpModal').style.display = 'flex';
}

function showAbout() {
    alert('天气数据爬取工具 v1.0.2 (备用版本)\n\n使用fetch API直接获取数据的版本。');
}

function closeModal() {
    document.getElementById('helpModal').style.display = 'none';
}
