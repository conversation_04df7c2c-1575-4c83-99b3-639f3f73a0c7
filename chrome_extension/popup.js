// 城市映射表
const CITY_MAPPING = {
    '海宁': 'haining',
    '杭州': 'hangzhou', 
    '金华': 'jinhua',
    '宁波': 'ningbo',
    '衢州': 'quzhou',
    '台州': 'taizhou',
    '温州': 'wenzhou',
    '诸暨': 'zhuji',
    '嘉兴': 'jiaxing',
    '绍兴': 'shaoxing',
    '湖州': 'huzhou',
    '丽水': 'lishui',
    '舟山': 'zhoushan'
};

// DOM元素
let startButton, progressSection, resultSection, progressFill, progressText, resultContent, downloadBtn;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    bindEvents();
    loadSettings();
});

function initializeElements() {
    startButton = document.getElementById('startScrape');
    progressSection = document.getElementById('progressSection');
    resultSection = document.getElementById('resultSection');
    progressFill = document.getElementById('progressFill');
    progressText = document.getElementById('progressText');
    resultContent = document.getElementById('resultContent');
    downloadBtn = document.getElementById('downloadBtn');
}

function bindEvents() {
    // 全选/清空按钮
    document.getElementById('selectAll').addEventListener('click', selectAllCities);
    document.getElementById('clearAll').addEventListener('click', clearAllCities);
    
    // 开始爬取按钮
    startButton.addEventListener('click', startScraping);
    
    // 下载按钮
    downloadBtn.addEventListener('click', downloadFile);
    
    // 帮助和关于按钮
    document.getElementById('helpBtn').addEventListener('click', showHelp);
    document.getElementById('aboutBtn').addEventListener('click', showAbout);
    
    // 模态框关闭
    document.querySelector('.close').addEventListener('click', closeModal);
    document.getElementById('helpModal').addEventListener('click', function(e) {
        if (e.target === this) closeModal();
    });
    
    // 保存设置
    document.getElementById('year').addEventListener('change', saveSettings);
    document.getElementById('month').addEventListener('change', saveSettings);
}

function selectAllCities() {
    document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
    });
    saveSettings();
}

function clearAllCities() {
    document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    saveSettings();
}

function getSelectedCities() {
    const selected = [];
    document.querySelectorAll('.city-item input[type="checkbox"]:checked').forEach(checkbox => {
        selected.push(checkbox.value);
    });
    return selected;
}

function startScraping() {
    const selectedCities = getSelectedCities();
    const year = parseInt(document.getElementById('year').value);
    const month = parseInt(document.getElementById('month').value);
    
    // 验证输入
    if (selectedCities.length === 0) {
        showError('请至少选择一个城市');
        return;
    }
    
    // 显示进度
    showProgress();
    startButton.disabled = true;
    
    // 开始爬取
    scrapeWeatherData(selectedCities, year, month);
}

function showProgress() {
    progressSection.style.display = 'block';
    resultSection.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '准备中...';
}

function updateProgress(percent, message) {
    progressFill.style.width = percent + '%';
    progressText.textContent = message;
}

function showResult(success, message, data = null) {
    progressSection.style.display = 'none';
    resultSection.style.display = 'block';
    startButton.disabled = false;
    
    resultContent.className = success ? 'result-success' : 'result-error';
    resultContent.textContent = message;
    
    if (success && data) {
        downloadBtn.style.display = 'block';
        downloadBtn.onclick = () => downloadExcel(data);
    } else {
        downloadBtn.style.display = 'none';
    }
}

function showError(message) {
    alert('错误: ' + message);
}

async function scrapeWeatherData(cities, year, month) {
    try {
        const allData = [];
        const totalCities = cities.length;
        
        for (let i = 0; i < cities.length; i++) {
            const chineseCity = cities[i];
            const pinyinCity = CITY_MAPPING[chineseCity];
            
            if (!pinyinCity) {
                console.warn(`未找到城市 ${chineseCity} 的拼音映射`);
                continue;
            }
            
            updateProgress(
                Math.round((i / totalCities) * 90), 
                `正在爬取 ${chineseCity} (${i + 1}/${totalCities})...`
            );
            
            try {
                const cityData = await scrapeCity(pinyinCity, year, month);
                
                // 将拼音城市名转换为中文
                cityData.forEach(record => {
                    record.city = chineseCity;
                });
                
                allData.push(...cityData);
                
                // 添加延迟避免请求过快
                if (i < cities.length - 1) {
                    await sleep(1000);
                }
                
            } catch (error) {
                console.error(`爬取 ${chineseCity} 失败:`, error);
            }
        }
        
        updateProgress(95, '正在整理数据...');
        
        if (allData.length > 0) {
            updateProgress(100, '爬取完成！');
            showResult(true, `成功爬取 ${allData.length} 条数据`, allData);
        } else {
            showResult(false, '未获取到任何数据，请检查网络连接或稍后重试');
        }
        
    } catch (error) {
        console.error('爬取失败:', error);
        showResult(false, '爬取失败: ' + error.message);
    }
}

async function scrapeCity(city, year, month) {
    try {
        // 直接使用fetch API获取数据
        const url = `https://www.tianqi24.com/lishi/${city}/${year}${month.toString().padStart(2, '0')}.html`;
        console.log('正在访问:', url);

        const response = await fetch(url, {
            method: 'GET',
            mode: 'cors',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();

        // 解析HTML数据
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 提取天气数据
        const weatherData = parseWeatherData(doc, city, year, month);

        console.log(`成功获取 ${weatherData.length} 条数据`);
        return weatherData;

    } catch (error) {
        console.error('爬取失败:', error);
        throw error;
    }
}

function parseWeatherData(doc, city, year, month) {
    const weatherData = [];

    try {
        // 查找天气数据列表
        const weatherList = doc.querySelectorAll('li');

        for (const li of weatherList) {
            const text = li.textContent.trim();

            // 使用正则表达式匹配日期格式 (如: 03-01)
            const dateMatch = text.match(/(\d{2})-(\d{2})/);
            if (!dateMatch) continue;

            const monthDay = dateMatch[0];
            const fullDate = `${year}-${monthDay}`;

            // 解析天气信息
            const weatherInfo = parseWeatherInfo(text, fullDate, city);
            if (weatherInfo) {
                weatherData.push(weatherInfo);
            }
        }

        // 如果上面的方法没有找到数据，尝试其他解析方法
        if (weatherData.length === 0) {
            return parseAlternativeFormat(doc, city, year, month);
        }

        return weatherData;

    } catch (error) {
        console.error('解析数据失败:', error);
        return [];
    }
}

function parseWeatherInfo(text, date, city) {
    try {
        // 提取温度信息
        const tempPattern = /(\d+)℃.*?(-?\d+)℃/;
        const tempMatch = text.match(tempPattern);

        if (!tempMatch) return null;

        const highTemp = parseInt(tempMatch[1]);
        const lowTemp = parseInt(tempMatch[2]);

        // 提取天气信息
        const weatherPart = text.replace(/^\d{2}-\d{2}/, '');

        // 查找天气关键词
        const weatherKeywords = ['晴', '多云', '阴', '雨', '小雨', '中雨', '大雨', '暴雨', '雪', '小雪', '中雪', '大雪', '雾', '霾', '沙尘'];
        const foundWeathers = [];

        for (const keyword of weatherKeywords) {
            if (weatherPart.includes(keyword)) {
                foundWeathers.push(keyword);
            }
        }

        const dayWeather = foundWeathers[0] || '未知';
        const nightWeather = foundWeathers[1] || foundWeathers[0] || '未知';

        // 获取星期信息
        const weekday = getWeekday(date);

        return {
            city: city,
            date: date,
            weekday: weekday,
            dayWeather: dayWeather,
            nightWeather: nightWeather,
            highTemp: highTemp,
            lowTemp: lowTemp
        };

    } catch (error) {
        console.error('解析天气信息失败:', error);
        return null;
    }
}

function parseAlternativeFormat(doc, city, year, month) {
    const weatherData = [];

    try {
        // 查找所有可能包含天气数据的元素
        const elements = doc.querySelectorAll('tr, div, li');

        for (const element of elements) {
            const text = element.textContent.trim();

            // 查找日期模式
            const datePattern = new RegExp(`${month.toString().padStart(2, '0')}-(\\d{2})`);
            const dateMatch = text.match(datePattern);

            if (dateMatch) {
                const day = dateMatch[1];
                const fullDate = `${year}-${month.toString().padStart(2, '0')}-${day}`;

                // 提取温度
                const tempPattern = /(\d+)℃.*?(-?\d+)℃/;
                const tempMatch = text.match(tempPattern);

                if (tempMatch) {
                    const highTemp = parseInt(tempMatch[1]);
                    const lowTemp = parseInt(tempMatch[2]);

                    // 提取天气
                    const weatherPattern = /(晴|多云|阴|雨|小雨|中雨|大雨|暴雨|雪|雾|霾|沙)/g;
                    const weatherMatches = text.match(weatherPattern) || [];

                    const dayWeather = weatherMatches[0] || '未知';
                    const nightWeather = weatherMatches[1] || dayWeather;

                    const weekday = getWeekday(fullDate);

                    weatherData.push({
                        city: city,
                        date: fullDate,
                        weekday: weekday,
                        dayWeather: dayWeather,
                        nightWeather: nightWeather,
                        highTemp: highTemp,
                        lowTemp: lowTemp
                    });
                }
            }
        }

    } catch (error) {
        console.error('备用解析失败:', error);
    }

    return weatherData;
}

function getWeekday(dateString) {
    try {
        const date = new Date(dateString);
        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        return weekdays[date.getDay()];
    } catch (error) {
        return '未知';
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function downloadExcel(data) {
    try {
        // 创建CSV内容
        const headers = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)'];
        let csvContent = headers.join(',') + '\n';
        
        data.forEach(row => {
            const csvRow = [
                row.city || '',
                row.date || '',
                row.weekday || '',
                row.dayWeather || '',
                row.nightWeather || '',
                row.highTemp || '',
                row.lowTemp || ''
            ].join(',');
            csvContent += csvRow + '\n';
        });
        
        // 添加BOM以支持中文
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // 生成文件名
        const now = new Date();
        const filename = `天气数据_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}_${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}.csv`;
        
        // 下载文件
        chrome.downloads.download({
            url: URL.createObjectURL(blob),
            filename: filename,
            saveAs: true
        });
        
    } catch (error) {
        console.error('下载失败:', error);
        showError('下载失败: ' + error.message);
    }
}

function saveSettings() {
    const settings = {
        year: document.getElementById('year').value,
        month: document.getElementById('month').value,
        cities: getSelectedCities()
    };
    
    chrome.storage.local.set({ weatherSettings: settings });
}

function loadSettings() {
    chrome.storage.local.get(['weatherSettings'], function(result) {
        if (result.weatherSettings) {
            const settings = result.weatherSettings;
            
            // 恢复年份月份
            if (settings.year) document.getElementById('year').value = settings.year;
            if (settings.month) document.getElementById('month').value = settings.month;
            
            // 恢复城市选择
            if (settings.cities) {
                document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = settings.cities.includes(checkbox.value);
                });
            }
        }
    });
}

function showHelp() {
    document.getElementById('helpModal').style.display = 'flex';
}

function showAbout() {
    alert('天气数据爬取工具 v1.0.0\n\n一个简单易用的天气数据获取工具，支持爬取历史天气数据并导出为Excel格式。\n\n开发者：天气数据爬取工具团队');
}

function closeModal() {
    document.getElementById('helpModal').style.display = 'none';
}
