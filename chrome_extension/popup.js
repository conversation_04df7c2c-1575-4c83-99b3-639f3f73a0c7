// 城市映射表
const CITY_MAPPING = {
    '海宁': 'haining',
    '杭州': 'hangzhou', 
    '金华': 'jinhua',
    '宁波': 'ningbo',
    '衢州': 'quzhou',
    '台州': 'taizhou',
    '温州': 'wenzhou',
    '诸暨': 'zhuji',
    '嘉兴': 'jiaxing',
    '绍兴': 'shaoxing',
    '湖州': 'huzhou',
    '丽水': 'lishui',
    '舟山': 'zhoushan'
};

// DOM元素
let startButton, progressSection, resultSection, progressFill, progressText, resultContent, downloadBtn;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    bindEvents();
    loadSettings();
});

function initializeElements() {
    startButton = document.getElementById('startScrape');
    progressSection = document.getElementById('progressSection');
    resultSection = document.getElementById('resultSection');
    progressFill = document.getElementById('progressFill');
    progressText = document.getElementById('progressText');
    resultContent = document.getElementById('resultContent');
    downloadBtn = document.getElementById('downloadBtn');
}

function bindEvents() {
    // 全选/清空按钮
    document.getElementById('selectAll').addEventListener('click', selectAllCities);
    document.getElementById('clearAll').addEventListener('click', clearAllCities);
    
    // 开始爬取按钮
    startButton.addEventListener('click', startScraping);
    
    // 下载按钮
    downloadBtn.addEventListener('click', downloadFile);
    
    // 帮助和关于按钮
    document.getElementById('helpBtn').addEventListener('click', showHelp);
    document.getElementById('aboutBtn').addEventListener('click', showAbout);
    
    // 模态框关闭
    document.querySelector('.close').addEventListener('click', closeModal);
    document.getElementById('helpModal').addEventListener('click', function(e) {
        if (e.target === this) closeModal();
    });
    
    // 保存设置
    document.getElementById('year').addEventListener('change', saveSettings);
    document.getElementById('month').addEventListener('change', saveSettings);
}

function selectAllCities() {
    document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
    });
    saveSettings();
}

function clearAllCities() {
    document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    saveSettings();
}

function getSelectedCities() {
    const selected = [];
    document.querySelectorAll('.city-item input[type="checkbox"]:checked').forEach(checkbox => {
        selected.push(checkbox.value);
    });
    return selected;
}

function startScraping() {
    const selectedCities = getSelectedCities();
    const year = parseInt(document.getElementById('year').value);
    const month = parseInt(document.getElementById('month').value);
    
    // 验证输入
    if (selectedCities.length === 0) {
        showError('请至少选择一个城市');
        return;
    }
    
    // 显示进度
    showProgress();
    startButton.disabled = true;
    
    // 开始爬取
    scrapeWeatherData(selectedCities, year, month);
}

function showProgress() {
    progressSection.style.display = 'block';
    resultSection.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '准备中...';
}

function updateProgress(percent, message) {
    progressFill.style.width = percent + '%';
    progressText.textContent = message;
}

function showResult(success, message, data = null) {
    progressSection.style.display = 'none';
    resultSection.style.display = 'block';
    startButton.disabled = false;
    
    resultContent.className = success ? 'result-success' : 'result-error';
    resultContent.textContent = message;
    
    if (success && data) {
        downloadBtn.style.display = 'block';
        downloadBtn.onclick = () => downloadExcel(data);
    } else {
        downloadBtn.style.display = 'none';
    }
}

function showError(message) {
    alert('错误: ' + message);
}

async function scrapeWeatherData(cities, year, month) {
    try {
        const allData = [];
        const totalCities = cities.length;
        
        for (let i = 0; i < cities.length; i++) {
            const chineseCity = cities[i];
            const pinyinCity = CITY_MAPPING[chineseCity];
            
            if (!pinyinCity) {
                console.warn(`未找到城市 ${chineseCity} 的拼音映射`);
                continue;
            }
            
            updateProgress(
                Math.round((i / totalCities) * 90), 
                `正在爬取 ${chineseCity} (${i + 1}/${totalCities})...`
            );
            
            try {
                const cityData = await scrapeCity(pinyinCity, year, month);
                
                // 将拼音城市名转换为中文
                cityData.forEach(record => {
                    record.city = chineseCity;
                });
                
                allData.push(...cityData);
                
                // 添加延迟避免请求过快
                if (i < cities.length - 1) {
                    await sleep(1000);
                }
                
            } catch (error) {
                console.error(`爬取 ${chineseCity} 失败:`, error);
            }
        }
        
        updateProgress(95, '正在整理数据...');
        
        if (allData.length > 0) {
            updateProgress(100, '爬取完成！');
            showResult(true, `成功爬取 ${allData.length} 条数据`, allData);
        } else {
            showResult(false, '未获取到任何数据，请检查网络连接或稍后重试');
        }
        
    } catch (error) {
        console.error('爬取失败:', error);
        showResult(false, '爬取失败: ' + error.message);
    }
}

async function scrapeCity(city, year, month) {
    return new Promise((resolve, reject) => {
        // 发送消息给content script
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'scrapeCity',
                city: city,
                year: year,
                month: month
            }, function(response) {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response && response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response ? response.error : '未知错误'));
                }
            });
        });
    });
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function downloadExcel(data) {
    try {
        // 创建CSV内容
        const headers = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)'];
        let csvContent = headers.join(',') + '\n';
        
        data.forEach(row => {
            const csvRow = [
                row.city || '',
                row.date || '',
                row.weekday || '',
                row.dayWeather || '',
                row.nightWeather || '',
                row.highTemp || '',
                row.lowTemp || ''
            ].join(',');
            csvContent += csvRow + '\n';
        });
        
        // 添加BOM以支持中文
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // 生成文件名
        const now = new Date();
        const filename = `天气数据_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}_${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}.csv`;
        
        // 下载文件
        chrome.downloads.download({
            url: URL.createObjectURL(blob),
            filename: filename,
            saveAs: true
        });
        
    } catch (error) {
        console.error('下载失败:', error);
        showError('下载失败: ' + error.message);
    }
}

function saveSettings() {
    const settings = {
        year: document.getElementById('year').value,
        month: document.getElementById('month').value,
        cities: getSelectedCities()
    };
    
    chrome.storage.local.set({ weatherSettings: settings });
}

function loadSettings() {
    chrome.storage.local.get(['weatherSettings'], function(result) {
        if (result.weatherSettings) {
            const settings = result.weatherSettings;
            
            // 恢复年份月份
            if (settings.year) document.getElementById('year').value = settings.year;
            if (settings.month) document.getElementById('month').value = settings.month;
            
            // 恢复城市选择
            if (settings.cities) {
                document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = settings.cities.includes(checkbox.value);
                });
            }
        }
    });
}

function showHelp() {
    document.getElementById('helpModal').style.display = 'flex';
}

function showAbout() {
    alert('天气数据爬取工具 v1.0.0\n\n一个简单易用的天气数据获取工具，支持爬取历史天气数据并导出为Excel格式。\n\n开发者：天气数据爬取工具团队');
}

function closeModal() {
    document.getElementById('helpModal').style.display = 'none';
}
