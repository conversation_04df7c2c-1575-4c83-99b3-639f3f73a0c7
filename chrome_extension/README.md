# 🌤️ 天气数据爬取工具 - Chrome浏览器插件

## 📦 安装方法

### 步骤1：准备插件文件
确保您已经下载了完整的 `chrome_extension` 文件夹。

### 步骤2：打开Chrome扩展程序页面
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 或者点击右上角三点菜单 → 更多工具 → 扩展程序

### 步骤3：启用开发者模式
在扩展程序页面右上角，打开"开发者模式"开关。

### 步骤4：加载插件
1. 点击"加载已解压的扩展程序"按钮
2. 选择 `chrome_extension` 文件夹
3. 点击"选择文件夹"

### 步骤5：确认安装
如果安装成功，您会在扩展程序列表中看到"天气数据爬取工具"。

## 🚀 使用方法

### 基本使用
1. 点击浏览器工具栏中的插件图标（如果没有显示，点击拼图图标查看所有扩展）
2. 在弹出窗口中选择要爬取的城市
3. 设置年份和月份
4. 点击"开始爬取"按钮
5. 等待爬取完成后下载CSV文件

### 功能特点
- ✅ 支持13个城市的天气数据爬取
- ✅ 友好的弹窗界面
- ✅ 实时进度显示
- ✅ 自动保存用户设置
- ✅ 一键下载CSV文件

## 🏙️ 支持的城市
海宁、杭州、金华、宁波、衢州、台州、温州、诸暨、嘉兴、绍兴、湖州、丽水、舟山

## 📊 输出格式
生成的CSV文件包含以下列：
- 城市
- 日期  
- 日期类型（星期几）
- 白天天气
- 晚上天气
- 最高温度(°C)
- 最低温度(°C)

## ⚠️ 注意事项

### 使用限制
- 需要访问 tianqi24.com 网站才能使用
- 每次只能爬取一个月的数据
- 请不要频繁使用，避免对服务器造成压力

### 权限说明
插件需要以下权限：
- **activeTab**: 访问当前标签页（用于数据爬取）
- **storage**: 保存用户设置
- **downloads**: 下载生成的文件
- **host_permissions**: 访问天气网站

### 故障排除

**问题1：插件无法加载**
- 确保已启用开发者模式
- 检查文件夹是否完整
- 尝试重新加载插件

**问题2：爬取失败**
- 检查网络连接
- 确认选择的城市和日期是否正确
- 尝试刷新页面后重试

**问题3：下载失败**
- 检查浏览器下载设置
- 确认有足够的磁盘空间
- 尝试手动保存数据

## 🔄 更新插件

如果插件有更新：
1. 下载新版本的插件文件
2. 在扩展程序页面点击插件的"重新加载"按钮
3. 或者删除旧插件后重新安装

## 🆘 技术支持

如果遇到问题：
1. 检查Chrome浏览器版本（建议88+）
2. 查看浏览器控制台错误信息
3. 尝试禁用其他可能冲突的扩展
4. 联系开发者获取帮助

## 📄 许可证

本插件仅供学习和个人使用。请遵守相关网站的使用条款，不要用于商业用途。

---

**🌤️ 感谢使用天气数据爬取工具Chrome插件！**
