// 城市映射表
const CITY_MAPPING = {
    '海宁': 'haining',
    '杭州': 'hangzhou', 
    '金华': 'jinhua',
    '宁波': 'ningbo',
    '衢州': 'quzhou',
    '台州': 'taizhou',
    '温州': 'wenzhou',
    '诸暨': 'zhuji',
    '嘉兴': 'jiaxing',
    '绍兴': 'shaoxing',
    '湖州': 'huzhou',
    '丽水': 'lishui',
    '舟山': 'zhoushan'
};

// DOM元素
let startButton, progressSection, resultSection, progressFill, progressText, resultContent, downloadBtn;

// 调试模式
const DEBUG_MODE = true;

// 日志函数
function debugLog(message, data = null) {
    if (DEBUG_MODE) {
        console.log(`🔧 [Debug] ${message}`, data || '');
    }
}

function errorLog(message, error = null) {
    console.error(`❌ [Error] ${message}`, error || '');
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    bindEvents();
    loadSettings();
});

function initializeElements() {
    startButton = document.getElementById('startScrape');
    progressSection = document.getElementById('progressSection');
    resultSection = document.getElementById('resultSection');
    progressFill = document.getElementById('progressFill');
    progressText = document.getElementById('progressText');
    resultContent = document.getElementById('resultContent');
    downloadBtn = document.getElementById('downloadBtn');
}

function bindEvents() {
    // 全选/清空按钮
    document.getElementById('selectAll').addEventListener('click', selectAllCities);
    document.getElementById('clearAll').addEventListener('click', clearAllCities);
    
    // 开始爬取按钮
    startButton.addEventListener('click', startScraping);
    
    // 下载按钮 - 在showResult中动态绑定具体的下载函数
    // downloadBtn的点击事件会在showResult函数中根据数据动态设置
    
    // 帮助和关于按钮
    document.getElementById('helpBtn').addEventListener('click', showHelp);
    document.getElementById('aboutBtn').addEventListener('click', showAbout);
    
    // 模态框关闭
    document.querySelector('.close').addEventListener('click', closeModal);
    document.getElementById('helpModal').addEventListener('click', function(e) {
        if (e.target === this) closeModal();
    });
    
    // 保存设置
    document.getElementById('year').addEventListener('change', saveSettings);
    document.getElementById('month').addEventListener('change', saveSettings);
}

function selectAllCities() {
    document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
    });
    saveSettings();
}

function clearAllCities() {
    document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    saveSettings();
}

function getSelectedCities() {
    const selected = [];
    document.querySelectorAll('.city-item input[type="checkbox"]:checked').forEach(checkbox => {
        selected.push(checkbox.value);
    });
    return selected;
}

async function startScraping() {
    const selectedCities = getSelectedCities();
    const year = parseInt(document.getElementById('year').value);
    const month = parseInt(document.getElementById('month').value);
    
    // 验证输入
    if (selectedCities.length === 0) {
        showError('请至少选择一个城市');
        return;
    }
    
    // 验证日期合理性
    const currentDate = new Date();
    const selectedDate = new Date(year, month - 1);
    
    if (selectedDate > currentDate) {
        showError('不能选择未来的日期', '请选择当前日期或之前的日期');
        return;
    }
    
    if (year < 2010) {
        showError('年份太早', '建议选择2010年之后的日期，数据更完整');
        return;
    }
    
    // 检查网络连接
    if (!navigator.onLine) {
        showError('网络连接异常', '请检查网络连接后重试');
        return;
    }
    
    // 显示进度和禁用按钮
    showProgress();
    startButton.disabled = true;
    startButton.textContent = '爬取中...';
    
    // 保存当前设置
    saveSettings();
    
    try {
        // 开始爬取
        await scrapeWeatherData(selectedCities, year, month);
    } catch (error) {
        console.error('爬取过程发生未捕获错误:', error);
        showResult(false, `爬取过程发生错误: ${error.message}`);
    } finally {
        // 恢复按钮状态
        startButton.disabled = false;
        startButton.textContent = '🚀 开始爬取';
    }
}

function showProgress() {
    progressSection.style.display = 'block';
    resultSection.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '准备中...';
}

function updateProgress(percent, message) {
    progressFill.style.width = percent + '%';
    progressText.textContent = message;
}

function showResult(success, message, data = null) {
    progressSection.style.display = 'none';
    resultSection.style.display = 'block';
    startButton.disabled = false;
    
    resultContent.className = success ? 'result-success' : 'result-error';
    
    // 清除之前的内容
    resultContent.innerHTML = '';
    
    // 格式化消息显示
    const messageDiv = document.createElement('div');
    if (typeof message === 'string' && message.includes('\n')) {
        // 多行消息，使用预格式化文本
        messageDiv.style.whiteSpace = 'pre-line';
        messageDiv.textContent = message;
    } else {
        messageDiv.style.whiteSpace = 'normal';
        messageDiv.textContent = message;
    }
    resultContent.appendChild(messageDiv);
    
    if (success && data && data.length > 0) {
        downloadBtn.style.display = 'block';
        
        // 动态绑定下载函数
        downloadBtn.onclick = function() {
            downloadExcel(data);
        };
        
        // 添加数据统计信息
        const statsDiv = document.createElement('div');
        statsDiv.style.marginTop = '10px';
        statsDiv.style.fontSize = '12px';
        statsDiv.style.color = '#666';
        
        const cityStats = {};
        data.forEach(item => {
            cityStats[item.city] = (cityStats[item.city] || 0) + 1;
        });
        
        const statsText = Object.entries(cityStats)
            .map(([city, count]) => `${city}: ${count}条`)
            .join(', ');
        
        statsDiv.textContent = `数据分布: ${statsText}`;
        resultContent.appendChild(statsDiv);
        
    } else {
        downloadBtn.style.display = 'none';
        
        // 为错误消息添加帮助信息
        if (!success) {
            const helpDiv = document.createElement('div');
            helpDiv.style.marginTop = '15px';
            helpDiv.style.padding = '10px';
            helpDiv.style.backgroundColor = '#f8f9fa';
            helpDiv.style.borderRadius = '4px';
            helpDiv.style.fontSize = '12px';
            helpDiv.style.color = '#495057';
            
            helpDiv.innerHTML = `
                <strong>💡 解决建议:</strong><br>
                • 检查网络连接是否正常<br>
                • 确认选择的日期是否有数据<br>
                • 尝试选择较近的月份<br>
                • 稍后重试或选择其他城市
            `;
            
            resultContent.appendChild(helpDiv);
        }
    }
}

function showError(message, details = null) {
    console.error('错误:', message, details);
    
    let fullMessage = message;
    if (details) {
        fullMessage += '\n\n详细信息:\n' + details;
    }
    
    // 使用更友好的错误提示
    const errorDiv = document.createElement('div');
    errorDiv.style.position = 'fixed';
    errorDiv.style.top = '20px';
    errorDiv.style.right = '20px';
    errorDiv.style.backgroundColor = '#f8d7da';
    errorDiv.style.color = '#721c24';
    errorDiv.style.padding = '12px 16px';
    errorDiv.style.borderRadius = '6px';
    errorDiv.style.border = '1px solid #f5c6cb';
    errorDiv.style.maxWidth = '300px';
    errorDiv.style.zIndex = '10000';
    errorDiv.style.fontSize = '14px';
    errorDiv.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
    
    errorDiv.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px;">❌ 操作失败</div>
        <div style="white-space: pre-line;">${fullMessage}</div>
        <div style="margin-top: 10px; text-align: right;">
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: none; border: none; color: #721c24; cursor: pointer; font-size: 12px;">
                关闭
            </button>
        </div>
    `;
    
    document.body.appendChild(errorDiv);
    
    // 5秒后自动关闭
    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.remove();
        }
    }, 5000);
}

async function scrapeWeatherData(cities, year, month) {
    try {
        const allData = [];
        const totalCities = cities.length;
        let successCount = 0;
        let errorMessages = [];

        for (let i = 0; i < cities.length; i++) {
            const chineseCity = cities[i];
            const pinyinCity = CITY_MAPPING[chineseCity];

            if (!pinyinCity) {
                console.warn(`未找到城市 ${chineseCity} 的拼音映射`);
                errorMessages.push(`${chineseCity}: 城市映射未找到`);
                continue;
            }

            updateProgress(
                Math.round((i / totalCities) * 85),
                `正在爬取 ${chineseCity} (${i + 1}/${totalCities})...`
            );

            try {
                const cityData = await scrapeCity(pinyinCity, year, month, chineseCity);

                if (cityData && cityData.length > 0) {
                    allData.push(...cityData);
                    successCount++;
                    console.log(`✓ ${chineseCity}: 获取到 ${cityData.length} 条数据`);
                } else {
                    errorMessages.push(`${chineseCity}: 未获取到数据`);
                    console.warn(`⚠️ ${chineseCity}: 未获取到数据`);
                }

                // 添加延迟避免请求过快
                if (i < cities.length - 1) {
                    await sleep(2000); // 增加延迟到2秒
                }

            } catch (error) {
                console.error(`❌ 爬取 ${chineseCity} 失败:`, error);
                errorMessages.push(`${chineseCity}: ${error.message}`);
            }
        }

        updateProgress(95, '正在整理数据...');

        // 准备结果消息
        let resultMessage = '';
        if (allData.length > 0) {
            resultMessage = `成功爬取 ${allData.length} 条数据 (${successCount}/${totalCities} 个城市)`;
            if (errorMessages.length > 0) {
                resultMessage += `\n\n部分失败:\n${errorMessages.slice(0, 3).join('\n')}`;
                if (errorMessages.length > 3) {
                    resultMessage += `\n... 还有 ${errorMessages.length - 3} 个错误`;
                }
            }
            updateProgress(100, '爬取完成！');
            showResult(true, resultMessage, allData);
        } else {
            resultMessage = '未获取到任何数据\n\n错误详情:\n' + errorMessages.slice(0, 5).join('\n');
            showResult(false, resultMessage);
        }

    } catch (error) {
        console.error('爬取过程发生错误:', error);
        showResult(false, `爬取失败: ${error.message}\n\n请检查网络连接或稍后重试`);
    }
}

async function scrapeCity(city, year, month, chineseName) {
    return new Promise((resolve, reject) => {
        // 修正URL格式：使用 /city/historyMM.html 格式
        const url = `https://www.tianqi24.com/${city}/history${month.toString().padStart(2, '0')}.html`;
        console.log(`🌐 正在访问: ${url}`);

        // 设置超时
        const timeout = setTimeout(() => {
            reject(new Error('请求超时 (30秒)'));
        }, 30000);

        // 创建隐藏的标签页
        chrome.tabs.create({
            url: url,
            active: false,
            pinned: false
        }, (tab) => {
            if (chrome.runtime.lastError) {
                clearTimeout(timeout);
                reject(new Error(`创建标签页失败: ${chrome.runtime.lastError.message}`));
                return;
            }

            let hasCompleted = false;

            // 监听页面加载状态
            const updateListener = function(tabId, changeInfo, tabInfo) {
                if (tabId === tab.id && changeInfo.status === 'complete' && !hasCompleted) {
                    hasCompleted = true;
                    chrome.tabs.onUpdated.removeListener(updateListener);
                    clearTimeout(timeout);

                    // 等待页面完全渲染
                    setTimeout(() => {
                        // 执行数据提取脚本
                        chrome.scripting.executeScript({
                            target: { tabId: tab.id },
                            func: extractWeatherData,
                            args: [city, year, month, chineseName]
                        }, (results) => {
                            // 关闭标签页
                            chrome.tabs.remove(tab.id, () => {
                                if (chrome.runtime.lastError) {
                                    console.warn('关闭标签页时出现警告:', chrome.runtime.lastError.message);
                                }
                            });

                            if (chrome.runtime.lastError) {
                                reject(new Error(`脚本执行失败: ${chrome.runtime.lastError.message}`));
                            } else if (results && results[0] && results[0].result) {
                                const data = results[0].result;
                                if (data.length > 0) {
                                    resolve(data);
                                } else {
                                    reject(new Error('页面中未找到天气数据'));
                                }
                            } else {
                                reject(new Error('脚本执行无结果'));
                            }
                        });
                    }, 2000); // 等待2秒让页面完全加载
                }
            };

            chrome.tabs.onUpdated.addListener(updateListener);

            // 监听标签页移除（用户手动关闭）
            chrome.tabs.onRemoved.addListener(function removeListener(tabId) {
                if (tabId === tab.id && !hasCompleted) {
                    hasCompleted = true;
                    chrome.tabs.onRemoved.removeListener(removeListener);
                    chrome.tabs.onUpdated.removeListener(updateListener);
                    clearTimeout(timeout);
                    reject(new Error('标签页被意外关闭'));
                }
            });
        });
    });
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 在目标页面中执行的数据提取函数 - 完整版本
function extractWeatherData(city, year, month, chineseName) {
    console.log(`🔍 开始提取 ${chineseName} 的天气数据...`);
    console.log(`URL: https://www.tianqi24.com/${city}/history${month.toString().padStart(2, '0')}.html`);

    try {
        const weatherData = [];

        // 输出调试信息
        console.log('页面标题:', document.title);
        console.log('页面URL:', window.location.href);

        // 方法1: 查找历史天气数据表格（主要方法）
        const tables = document.querySelectorAll('table');
        console.log(`找到 ${tables.length} 个表格`);

        for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
            const table = tables[tableIndex];
            const rows = table.querySelectorAll('tr');
            console.log(`表格 ${tableIndex + 1}: ${rows.length} 行`);

            for (let i = 1; i < rows.length; i++) { // 跳过表头
                const row = rows[i];
                const cells = row.querySelectorAll('td');

                if (cells.length >= 3) {
                    const rowText = row.textContent.trim();

                    // 检查是否包含当前月份的日期
                    const datePattern = new RegExp(`${month.toString().padStart(2, '0')}-(\\d{2})`);
                    const dateMatch = rowText.match(datePattern);

                    if (dateMatch) {
                        console.log(`找到日期行: ${rowText.substring(0, 100)}`);

                        const dayStr = dateMatch[1];
                        const fullDate = `${year}-${month.toString().padStart(2, '0')}-${dayStr}`;

                        // 提取温度信息
                        const tempPatterns = [
                            /(\d+)℃[^\d]*(-?\d+)℃/,           // 标准格式: 15℃/-2℃
                            /(\d+)°[^\d]*(-?\d+)°/,            // 度数格式: 15°/-2°
                            /高温[^\d]*(\d+)[^\d]*低温[^\d]*(-?\d+)/, // 中文格式
                            /最高[^\d]*(\d+)[^\d]*最低[^\d]*(-?\d+)/, // 中文格式2
                            /(\d+)[^\d]*\/[^\d]*(-?\d+)/       // 斜杠格式: 15/-2
                        ];

                        let tempMatch = null;
                        for (const pattern of tempPatterns) {
                            tempMatch = rowText.match(pattern);
                            if (tempMatch) break;
                        }

                        if (tempMatch) {
                            const highTemp = parseInt(tempMatch[1]);
                            const lowTemp = parseInt(tempMatch[2]);

                            // 验证温度合理性
                            if (highTemp >= -50 && highTemp <= 60 &&
                                lowTemp >= -50 && lowTemp <= 60 &&
                                lowTemp <= highTemp) {

                                // 提取天气信息
                                const weatherKeywords = [
                                    '晴', '多云', '阴', '雨', '小雨', '中雨', '大雨', '暴雨', '雷雨',
                                    '雪', '小雪', '中雪', '大雪', '雨夹雪', '冰雹',
                                    '雾', '霾', '沙尘', '浮尘', '扬沙', '沙尘暴'
                                ];

                                let dayWeather = '未知';
                                let nightWeather = '未知';

                                // 查找天气关键词
                                const foundWeathers = [];
                                for (const keyword of weatherKeywords) {
                                    if (rowText.includes(keyword) && !foundWeathers.includes(keyword)) {
                                        foundWeathers.push(keyword);
                                    }
                                }

                                if (foundWeathers.length >= 2) {
                                    dayWeather = foundWeathers[0];
                                    nightWeather = foundWeathers[1];
                                } else if (foundWeathers.length === 1) {
                                    dayWeather = nightWeather = foundWeathers[0];
                                }

                                // 获取星期信息
                                const dateObj = new Date(fullDate);
                                const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                                const weekday = weekdays[dateObj.getDay()];

                                weatherData.push({
                                    city: chineseName,
                                    date: fullDate,
                                    weekday: weekday,
                                    dayWeather: dayWeather,
                                    nightWeather: nightWeather,
                                    highTemp: highTemp,
                                    lowTemp: lowTemp
                                });

                                console.log(`✓ 提取成功: ${fullDate} ${dayWeather} ${highTemp}℃/${lowTemp}℃`);
                            } else {
                                console.warn(`温度数据异常: ${fullDate}, 高温:${highTemp}, 低温:${lowTemp}`);
                            }
                        } else {
                            console.warn(`未找到温度信息: ${rowText.substring(0, 50)}`);
                        }
                    }
                }
            }
        }

        // 方法2: 如果表格方法失败，尝试从页面文本中提取
        if (weatherData.length === 0) {
            console.log('表格解析失败，尝试文本解析...');

            const allText = document.body.textContent;
            const lines = allText.split('\n');

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (trimmedLine.length < 10 || trimmedLine.length > 200) continue;

                const extracted = extractFromText(trimmedLine, year, month, chineseName);
                if (extracted) {
                    weatherData.push(extracted);
                }
            }
        }

        // 去重和排序
        const uniqueData = removeDuplicates(weatherData);
        const sortedData = uniqueData.sort((a, b) => new Date(a.date) - new Date(b.date));

        console.log(`🎉 提取完成，共获得 ${sortedData.length} 条数据`);

        if (sortedData.length > 0) {
            console.log('前3条数据示例:', sortedData.slice(0, 3));
        } else {
            console.log('❌ 未提取到任何数据');
            // 输出页面主要内容用于调试
            const bodyText = document.body.textContent;
            const lines = bodyText.split('\n').filter(line => line.trim().length > 0);
            console.log('页面主要文本内容（前10行）:');
            lines.slice(0, 10).forEach((line, index) => {
                console.log(`${index + 1}: ${line.trim().substring(0, 100)}`);
            });
        }

        return sortedData;

    } catch (error) {
        console.error('❌ 数据提取失败:', error);
        return [];
    }
}

// 从文本中提取天气信息的辅助函数
function extractFromText(text, year, month, chineseName) {
    try {
        // 只匹配当前月份的日期格式: MM-DD
        const monthStr = month.toString().padStart(2, '0');
        const datePattern = new RegExp(`${monthStr}-(\\d{2})`);
        const dateMatch = text.match(datePattern);

        if (!dateMatch) return null;

        const dayStr = dateMatch[1];
        const fullDate = `${year}-${monthStr}-${dayStr}`;

        // 验证日期有效性
        const dateObj = new Date(fullDate);
        if (isNaN(dateObj.getTime()) || dateObj.getMonth() + 1 !== month) {
            return null;
        }

        // 提取温度信息
        const tempPattern = /(\d+)℃[^\d]*(-?\d+)℃/;
        const tempMatch = text.match(tempPattern);

        if (!tempMatch) return null;

        const highTemp = parseInt(tempMatch[1]);
        const lowTemp = parseInt(tempMatch[2]);

        // 验证温度合理性
        if (highTemp < -50 || highTemp > 60 || lowTemp < -50 || lowTemp > 60 || lowTemp > highTemp) {
            return null;
        }

        // 提取天气信息
        const weatherKeywords = ['晴', '多云', '阴', '雨', '小雨', '中雨', '大雨', '暴雨', '雷雨', '雪', '小雪', '中雪', '大雪', '雾', '霾'];
        const foundWeathers = [];

        for (const keyword of weatherKeywords) {
            if (text.includes(keyword) && !foundWeathers.includes(keyword)) {
                foundWeathers.push(keyword);
            }
        }

        const dayWeather = foundWeathers[0] || '未知';
        const nightWeather = foundWeathers[1] || foundWeathers[0] || '未知';

        // 获取星期信息
        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        const weekday = weekdays[dateObj.getDay()];

        return {
            city: chineseName,
            date: fullDate,
            weekday: weekday,
            dayWeather: dayWeather,
            nightWeather: nightWeather,
            highTemp: highTemp,
            lowTemp: lowTemp
        };

    } catch (error) {
        console.error('文本解析错误:', error);
        return null;
    }
}

// 去重函数
function removeDuplicates(weatherData) {
    const seen = new Set();
    return weatherData.filter(item => {
        const key = `${item.date}-${item.highTemp}-${item.lowTemp}`;
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
}

function downloadExcel(data) {
    try {
        // 创建CSV内容
        const headers = ['城市', '日期', '日期类型', '白天天气', '晚上天气', '最高温度(°C)', '最低温度(°C)'];
        let csvContent = headers.join(',') + '\n';

        data.forEach(row => {
            const csvRow = [
                row.city || '',
                row.date || '',
                row.weekday || '',
                row.dayWeather || '',
                row.nightWeather || '',
                row.highTemp || '',
                row.lowTemp || ''
            ].join(',');
            csvContent += csvRow + '\n';
        });

        // 添加BOM以支持中文
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

        // 生成文件名
        const now = new Date();
        const filename = `天气数据_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}_${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}.csv`;

        // 下载文件
        chrome.downloads.download({
            url: URL.createObjectURL(blob),
            filename: filename,
            saveAs: true
        });

    } catch (error) {
        console.error('下载失败:', error);
        showError('下载失败: ' + error.message);
    }
}

function saveSettings() {
    const settings = {
        year: document.getElementById('year').value,
        month: document.getElementById('month').value,
        cities: getSelectedCities()
    };

    chrome.storage.local.set({ weatherSettings: settings });
}

function loadSettings() {
    chrome.storage.local.get(['weatherSettings'], function(result) {
        if (result.weatherSettings) {
            const settings = result.weatherSettings;

            // 恢复年份月份
            if (settings.year) document.getElementById('year').value = settings.year;
            if (settings.month) document.getElementById('month').value = settings.month;

            // 恢复城市选择
            if (settings.cities) {
                document.querySelectorAll('.city-item input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = settings.cities.includes(checkbox.value);
                });
            }
        }
    });
}

function showHelp() {
    document.getElementById('helpModal').style.display = 'flex';
}

function showAbout() {
    alert('天气数据爬取工具 v1.0.0\n\n一个简单易用的天气数据获取工具，支持爬取历史天气数据并导出为Excel格式。\n\n开发者：天气数据爬取工具团队');
}

function closeModal() {
    document.getElementById('helpModal').style.display = 'none';
}
