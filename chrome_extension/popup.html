<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气数据爬取工具</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">🌤️</div>
            <h1>天气数据爬取工具</h1>
            <p class="subtitle">简单易用的天气数据获取工具</p>
        </div>

        <!-- 主要内容 -->
        <div class="content">
            <!-- 城市选择 -->
            <div class="section">
                <h3>选择城市</h3>
                <div class="city-grid">
                    <label class="city-item">
                        <input type="checkbox" value="嘉兴" checked>
                        <span>嘉兴</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="杭州">
                        <span>杭州</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="宁波">
                        <span>宁波</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="温州">
                        <span>温州</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="绍兴">
                        <span>绍兴</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="金华">
                        <span>金华</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="台州">
                        <span>台州</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="湖州">
                        <span>湖州</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="衢州">
                        <span>衢州</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="丽水">
                        <span>丽水</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="舟山">
                        <span>舟山</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="海宁">
                        <span>海宁</span>
                    </label>
                    <label class="city-item">
                        <input type="checkbox" value="诸暨">
                        <span>诸暨</span>
                    </label>
                </div>
                <div class="city-controls">
                    <button id="selectAll" class="btn-small">全选</button>
                    <button id="clearAll" class="btn-small">清空</button>
                </div>
            </div>

            <!-- 时间选择 -->
            <div class="section">
                <h3>选择时间</h3>
                <div class="time-inputs">
                    <div class="input-group">
                        <label>年份</label>
                        <select id="year">
                            <option value="2024" selected>2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                            <option value="2021">2021</option>
                            <option value="2020">2020</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>月份</label>
                        <select id="month">
                            <option value="1">1月</option>
                            <option value="2">2月</option>
                            <option value="3" selected>3月</option>
                            <option value="4">4月</option>
                            <option value="5">5月</option>
                            <option value="6">6月</option>
                            <option value="7">7月</option>
                            <option value="8">8月</option>
                            <option value="9">9月</option>
                            <option value="10">10月</option>
                            <option value="11">11月</option>
                            <option value="12">12月</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="section">
                <button id="startScrape" class="btn-primary">
                    <span class="btn-icon">🚀</span>
                    开始爬取
                </button>
            </div>

            <!-- 进度显示 -->
            <div id="progressSection" class="section" style="display: none;">
                <h3>爬取进度</h3>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText" class="progress-text">准备中...</div>
            </div>

            <!-- 结果显示 -->
            <div id="resultSection" class="section" style="display: none;">
                <h3>爬取结果</h3>
                <div id="resultContent"></div>
                <button id="downloadBtn" class="btn-success" style="display: none;">
                    <span class="btn-icon">📥</span>
                    下载Excel文件
                </button>
            </div>
        </div>

        <!-- 底部 -->
        <div class="footer">
            <div class="help-links">
                <a href="#" id="helpBtn">使用帮助</a>
                <a href="#" id="aboutBtn">关于</a>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>使用帮助</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <h4>使用步骤：</h4>
                <ol>
                    <li>选择要爬取的城市（可多选）</li>
                    <li>设置年份和月份</li>
                    <li>点击"开始爬取"按钮</li>
                    <li>等待爬取完成</li>
                    <li>下载生成的Excel文件</li>
                </ol>
                
                <h4>注意事项：</h4>
                <ul>
                    <li>请确保网络连接正常</li>
                    <li>爬取过程可能需要几分钟</li>
                    <li>请不要频繁使用，避免对服务器造成压力</li>
                </ul>
                
                <h4>数据格式：</h4>
                <p>生成的Excel文件包含：城市、日期、日期类型、白天天气、晚上天气、最高温度、最低温度等信息。</p>
            </div>
        </div>
    </div>

    <script src="popup_fixed.js"></script>
</body>
</html>
