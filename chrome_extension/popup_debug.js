// 调试版本 - 输出详细的页面结构信息

// 在目标页面中执行的调试数据提取函数
function extractWeatherData(city, year, month, chineseName) {
    console.log(`🔍 开始调试提取 ${chineseName} 的天气数据...`);
    console.log(`URL应该是: https://www.tianqi24.com/${city}/history${month.toString().padStart(2, '0')}.html`);
    
    try {
        // 输出页面基本信息
        console.log('页面标题:', document.title);
        console.log('页面URL:', window.location.href);
        
        // 查找所有表格
        const tables = document.querySelectorAll('table');
        console.log(`找到 ${tables.length} 个表格`);
        
        tables.forEach((table, index) => {
            console.log(`表格 ${index + 1}:`);
            const rows = table.querySelectorAll('tr');
            console.log(`  - 行数: ${rows.length}`);
            
            // 输出前几行的内容
            for (let i = 0; i < Math.min(rows.length, 5); i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td, th');
                console.log(`  - 第${i + 1}行 (${cells.length}列):`, row.textContent.trim().substring(0, 100));
            }
        });
        
        // 查找包含日期的元素
        const dateElements = document.querySelectorAll('*');
        const datePattern = new RegExp(`${month.toString().padStart(2, '0')}-(\\d{2})`);
        let dateCount = 0;
        
        dateElements.forEach(element => {
            const text = element.textContent.trim();
            if (datePattern.test(text) && text.length < 200) {
                dateCount++;
                if (dateCount <= 10) { // 只输出前10个
                    console.log(`包含日期的元素 ${dateCount}:`, text.substring(0, 150));
                }
            }
        });
        
        console.log(`总共找到 ${dateCount} 个包含日期的元素`);
        
        // 尝试实际提取数据
        const weatherData = [];
        
        // 方法1: 表格提取
        for (const table of tables) {
            const rows = table.querySelectorAll('tr');
            
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td');
                
                if (cells.length >= 3) {
                    const rowText = row.textContent.trim();
                    console.log(`处理行 ${i}:`, rowText.substring(0, 100));
                    
                    // 检查是否包含日期
                    const dateMatch = rowText.match(datePattern);
                    if (dateMatch) {
                        console.log(`  - 找到日期: ${dateMatch[0]}`);
                        
                        // 检查是否包含温度
                        const tempMatch = rowText.match(/(\d+)℃[^\d]*(-?\d+)℃/);
                        if (tempMatch) {
                            console.log(`  - 找到温度: 高温${tempMatch[1]}℃, 低温${tempMatch[2]}℃`);
                            
                            const dayStr = dateMatch[1];
                            const fullDate = `${year}-${month.toString().padStart(2, '0')}-${dayStr}`;
                            
                            // 提取天气
                            const weatherKeywords = ['晴', '多云', '阴', '雨', '小雨', '中雨', '大雨', '暴雨', '雷雨', '雪', '雾', '霾'];
                            let dayWeather = '未知';
                            let nightWeather = '未知';
                            
                            for (const keyword of weatherKeywords) {
                                if (rowText.includes(keyword)) {
                                    dayWeather = nightWeather = keyword;
                                    break;
                                }
                            }
                            
                            console.log(`  - 找到天气: ${dayWeather}`);
                            
                            const dateObj = new Date(fullDate);
                            const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                            const weekday = weekdays[dateObj.getDay()];
                            
                            weatherData.push({
                                city: chineseName,
                                date: fullDate,
                                weekday: weekday,
                                dayWeather: dayWeather,
                                nightWeather: nightWeather,
                                highTemp: parseInt(tempMatch[1]),
                                lowTemp: parseInt(tempMatch[2])
                            });
                            
                            console.log(`  - ✓ 成功提取数据`);
                        } else {
                            console.log(`  - ❌ 未找到温度信息`);
                        }
                    }
                }
            }
        }
        
        console.log(`🎉 调试提取完成，共获得 ${weatherData.length} 条数据`);
        
        if (weatherData.length > 0) {
            console.log('提取的数据示例:', weatherData.slice(0, 3));
        } else {
            console.log('❌ 未提取到任何数据，页面结构可能与预期不符');
            
            // 输出页面的主要文本内容用于分析
            const bodyText = document.body.textContent;
            const lines = bodyText.split('\n').filter(line => line.trim().length > 0);
            console.log('页面主要文本内容（前20行）:');
            lines.slice(0, 20).forEach((line, index) => {
                console.log(`${index + 1}: ${line.trim().substring(0, 100)}`);
            });
        }
        
        return weatherData;
        
    } catch (error) {
        console.error('❌ 调试提取失败:', error);
        return [];
    }
}
